<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>情报数据 - 蜜罐管理平台</title>
    <link href="/static/libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="/static/libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/dashboard.css" rel="stylesheet">
    <style>
        /* 侧边栏导航样式 */
        .main-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 260px;
            height: 100vh;
            background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar-brand {
            padding: 1.5rem 1.25rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-brand a {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.125rem;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-section {
            margin-bottom: 1.5rem;
        }

        .nav-section-title {
            padding: 0.5rem 1.25rem;
            font-size: 0.75rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.6);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.5rem;
        }

        .nav-item {
            margin: 0 0.75rem;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(4px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .nav-link i {
            width: 20px;
            text-align: center;
            font-size: 1rem;
        }

        .sidebar-user {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 1rem 1.25rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(0, 0, 0, 0.2);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: white;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
        }

        .user-info:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .user-avatar {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            font-size: 0.875rem;
            line-height: 1.2;
        }

        .user-role {
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.7);
        }

        /* 主内容区域调整为白色 */
        .main-content {
            margin-left: 260px;
            min-height: 100vh;
            background: #ffffff;
        }

        /* 容器背景调整 */
        .container-fluid {
            background: #ffffff;
        }

        /* 页面标题区域调整 */
        .main-content h2 {
            color: #5a5c69 !important;
        }

        .main-content .text-muted {
            color: #858796 !important;
        }

        /* 服务状态警告框调整 */
        .alert-info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }

        /* 统计卡片样式调整为白色主题 */
        .card {
            background: #ffffff;
            border: 1px solid #e3e6f0;
            color: #5a5c69;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }

        .card-body {
            background: #ffffff;
            color: #5a5c69;
        }

        .card .text-gray-800 {
            color: #5a5c69 !important;
        }

        .card .text-gray-300 {
            color: #dddfeb !important;
        }

        /* 图表和数据分析卡片调整 */
        .card.shadow {
            background: #ffffff;
            border: 1px solid #e3e6f0;
        }

        .card.shadow .card-header {
            background: #f8f9fc;
            border-bottom: 1px solid #e3e6f0;
            color: #5a5c69;
        }

        .card.shadow .card-body {
            background: #ffffff;
            color: #5a5c69;
        }

        /* 表格样式调整 */
        .table {
            background: #ffffff;
            color: #5a5c69;
        }

        .table thead th {
            background: #f8f9fc;
            color: #5a5c69;
            border-bottom: 1px solid #e3e6f0;
        }

        .table tbody td {
            background: #ffffff;
            color: #5a5c69;
            border-bottom: 1px solid #e3e6f0;
        }

        .table tbody tr:nth-child(even) td {
            background: #f8f9fc;
        }

        .table tbody tr:hover td {
            background: #eaecf4;
            color: #5a5c69;
        }

        .table.table-bordered {
            border: 1px solid #e3e6f0;
        }

        .table.table-bordered thead th {
            border-color: #e3e6f0;
        }

        .table.table-bordered tbody td {
            border-color: #e3e6f0;
        }

        .table.table-sm {
            background: #ffffff;
            color: #5a5c69;
        }

        /* 分析标签页调整 */
        .nav-tabs {
            background: #ffffff;
            border-bottom: 1px solid #e3e6f0;
        }

        .nav-tabs .nav-link {
            background: #ffffff;
            border: 1px solid #e3e6f0;
            color: #5a5c69;
        }

        .nav-tabs .nav-link:hover {
            background: #f8f9fc;
            border-color: #e3e6f0;
            color: #5a5c69;
        }

        .nav-tabs .nav-link.active {
            background: #ffffff;
            border-color: #e3e6f0 #e3e6f0 #ffffff;
            color: #4e73df;
        }

        .tab-content {
            background: #ffffff;
        }

        .tab-pane {
            background: #ffffff;
            color: #5a5c69;
        }

        /* 时间范围选择器和过滤器调整 */
        .form-select {
            background: #ffffff;
            border: 1px solid #d1d3e2;
            color: #5a5c69;
        }

        .form-select:focus {
            background: #ffffff;
            border-color: #bac8f3;
            color: #5a5c69;
        }

        /* 统计摘要卡片调整 */
        .stat-card {
            background: #ffffff;
            border: 1px solid #e3e6f0;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .stat-card h6 {
            color: #858796;
            font-size: 0.75rem;
            text-transform: uppercase;
            margin-bottom: 0.5rem;
        }

        .stat-card h4 {
            color: #5a5c69;
            font-weight: 700;
            margin: 0;
        }

        /* 趋势分析区域调整 */
        #trend-summary {
            background: #ffffff;
            color: #5a5c69;
        }

        #peak-hours {
            background: #ffffff;
            color: #5a5c69;
        }

        /* 地理分布和攻击类型分析调整 */
        #country-stats,
        #top-attackers-table,
        #type-distribution {
            background: #ffffff;
            color: #5a5c69;
        }

        #threat-assessment {
            background: #ffffff;
            color: #5a5c69;
        }

        /* 加载状态调整 */
        .text-center {
            color: #858796;
            background: #ffffff;
        }

        /* 徽章颜色保持不变 */
        .badge {
            /* 保持原有的状态颜色 */
        }

        /* 移动端适配 */
        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1001;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 0.5rem;
            padding: 0.5rem;
            font-size: 1.25rem;
        }

        @media (max-width: 768px) {
            .main-sidebar {
                transform: translateX(-100%);
            }

            .main-sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar-toggle {
                display: block;
            }
        }
    </style>
</head>
<body>
    <!-- 移动端侧边栏切换按钮 -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="bi bi-list"></i>
    </button>

    <!-- 左侧导航栏 -->
    <div class="main-sidebar" id="mainSidebar">
        <!-- 品牌标识 -->
        <div class="sidebar-brand">
            <a href="/dashboard">
                <i class="bi bi-shield-check"></i>
                蜜罐管理平台
            </a>
        </div>

        <!-- 导航菜单 -->
        <nav class="sidebar-nav">
            <!-- 主要功能 -->
            <div class="nav-section">
                <div class="nav-section-title">主要功能</div>
                <div class="nav-item">
                    <a class="nav-link" href="/dashboard">
                        <i class="bi bi-speedometer2"></i>
                        <span>仪表板</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/nodes">
                        <i class="bi bi-hdd-network"></i>
                        <span>节点管理</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/templates">
                        <i class="bi bi-layers"></i>
                        <span>模板管理</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/deployments">
                        <i class="bi bi-play-circle"></i>
                        <span>部署管理</span>
                    </a>
                </div>
            </div>

            <!-- 数据分析 -->
            <div class="nav-section">
                <div class="nav-section-title">数据分析</div>
                <div class="nav-item">
                    <a class="nav-link active" href="/intelligence">
                        <i class="bi bi-shield-exclamation"></i>
                        <span>情报数据</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/statistics">
                        <i class="bi bi-bar-chart"></i>
                        <span>统计分析</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/reports">
                        <i class="bi bi-file-text"></i>
                        <span>报表生成</span>
                    </a>
                </div>
            </div>

            <!-- 系统管理 -->
            <div class="nav-section">
                <div class="nav-section-title">系统管理</div>
                <div class="nav-item">
                    <a class="nav-link" href="/users">
                        <i class="bi bi-people"></i>
                        <span>用户管理</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/settings">
                        <i class="bi bi-gear"></i>
                        <span>系统设置</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/logs">
                        <i class="bi bi-journal-text"></i>
                        <span>系统日志</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- 用户信息 -->
        <div class="sidebar-user">
            <div class="user-info" onclick="toggleUserMenu()">
                <div class="user-avatar">
                    <i class="bi bi-person"></i>
                </div>
                <div class="user-details">
                    <div class="user-name" id="sidebar-username">管理员</div>
                    <div class="user-role">系统管理员</div>
                </div>
                <i class="bi bi-chevron-up"></i>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
        <div class="container-fluid mt-4">
            <!-- 页面标题和操作按钮 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <h2 class="mb-0">
                        <i class="bi bi-shield-exclamation me-2"></i>情报数据
                    </h2>
                    <p class="text-muted">攻击数据统计与分析</p>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-outline-secondary" onclick="refreshData()">
                        <i class="bi bi-arrow-clockwise me-2"></i>刷新数据
                    </button>
                </div>
            </div>

            <!-- 服务状态 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-info" role="alert" id="service-status">
                        <span class="spinner-border spinner-border-sm me-2"></span>正在检查DecoyWatch服务状态...
                    </div>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        总攻击次数
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-attacks">
                                        <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-shield-exclamation text-gray-300" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        今日攻击
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="today-attacks">
                                        <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-calendar-day text-gray-300" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        独立IP数
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="unique-ips">
                                        <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-geo text-gray-300" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        攻击类型
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="attack-types">
                                        <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-list-ul text-gray-300" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-pie-chart me-2"></i>攻击类型分布
                            </h6>
                        </div>
                        <div class="card-body">
                            <canvas id="attack-types-chart" width="400" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-graph-up me-2"></i>24小时攻击趋势
                            </h6>
                        </div>
                        <div class="card-body">
                            <canvas id="hourly-attacks-chart" width="400" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-list-ol me-2"></i>Top攻击源IP
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>IP地址</th>
                                            <th>攻击次数</th>
                                            <th>地理位置</th>
                                        </tr>
                                    </thead>
                                    <tbody id="top-ips-table">
                                        <tr>
                                            <td colspan="3" class="text-center">
                                                <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-list-ol me-2"></i>Top目标端口
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>端口</th>
                                            <th>攻击次数</th>
                                            <th>服务</th>
                                        </tr>
                                    </thead>
                                    <tbody id="top-ports-table">
                                        <tr>
                                            <td colspan="3" class="text-center">
                                                <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 攻击数据分析 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-graph-up me-2"></i>攻击数据分析
                            </h6>
                            <div class="d-flex gap-2">
                                <select class="form-select form-select-sm time-range-selector" style="width: auto;">
                                    <option value="1h">最近1小时</option>
                                    <option value="24h" selected>最近24小时</option>
                                    <option value="7d">最近7天</option>
                                    <option value="30d">最近30天</option>
                                </select>
                                <select class="form-select form-select-sm" id="node-filter" style="width: auto;">
                                    <option value="">所有节点</option>
                                    <!-- 节点选项将通过JavaScript动态加载 -->
                                </select>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 分析标签页 -->
                            <ul class="nav nav-tabs" id="analysisTab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="trend-tab" data-bs-toggle="tab"
                                            data-bs-target="#trend-analysis" type="button" role="tab">
                                        <i class="bi bi-graph-up me-1"></i>趋势分析
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="geographic-tab" data-bs-toggle="tab"
                                            data-bs-target="#geographic-analysis" type="button" role="tab">
                                        <i class="bi bi-geo-alt me-1"></i>地理分布
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="attack-types-tab" data-bs-toggle="tab"
                                            data-bs-target="#attack-types-analysis" type="button" role="tab">
                                        <i class="bi bi-shield-exclamation me-1"></i>攻击类型
                                    </button>
                                </li>
                            </ul>

                            <!-- 分析内容 -->
                            <div class="tab-content mt-3" id="analysisTabContent">
                                <!-- 趋势分析 -->
                                <div class="tab-pane fade show active" id="trend-analysis" role="tabpanel">
                                    <div class="row mb-3">
                                        <div class="col-12">
                                            <div id="trend-summary">
                                                <div class="text-center">
                                                    <span class="spinner-border spinner-border-sm me-2"></span>加载趋势分析数据...
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0">攻击趋势图</h6>
                                                </div>
                                                <div class="card-body">
                                                    <canvas id="trend-chart" width="400" height="200"></canvas>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0">攻击高峰时段</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div id="peak-hours">
                                                        <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 地理分布分析 -->
                                <div class="tab-pane fade" id="geographic-analysis" role="tabpanel">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0">国家/地区统计</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="table-responsive">
                                                        <table class="table table-sm">
                                                            <thead>
                                                                <tr>
                                                                    <th>国家/地区</th>
                                                                    <th>攻击次数</th>
                                                                    <th>唯一IP</th>
                                                                    <th>占比</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="country-stats">
                                                                <tr>
                                                                    <td colspan="4" class="text-center">
                                                                        <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0">顶级攻击者IP</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="table-responsive">
                                                        <table class="table table-sm">
                                                            <thead>
                                                                <tr>
                                                                    <th>IP地址</th>
                                                                    <th>攻击次数</th>
                                                                    <th>国家</th>
                                                                    <th>攻击类型</th>
                                                                    <th>最后活动</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="top-attackers">
                                                                <tr>
                                                                    <td colspan="5" class="text-center">
                                                                        <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 攻击类型分析 -->
                                <div class="tab-pane fade" id="attack-types-analysis" role="tabpanel">
                                    <div class="row mb-3">
                                        <div class="col-12">
                                            <div id="threat-assessment">
                                                <div class="text-center">
                                                    <span class="spinner-border spinner-border-sm me-2"></span>加载威胁评估数据...
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0">攻击类型分布</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="table-responsive">
                                                        <table class="table table-sm">
                                                            <thead>
                                                                <tr>
                                                                    <th>攻击类型</th>
                                                                    <th>攻击次数</th>
                                                                    <th>占比</th>
                                                                    <th>描述</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="type-distribution">
                                                                <tr>
                                                                    <td colspan="4" class="text-center">
                                                                        <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <script src="/static/libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="/static/libs/chart.js/chart.min.js"></script>
    <script src="/static/js/intelligence.js"></script>
</body>
</html>
