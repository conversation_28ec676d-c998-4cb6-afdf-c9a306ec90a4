<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}} - 蜜罐管理平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="/static/libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="/static/libs/chart.js/chart.min.js"></script>
    <!-- Custom CSS -->
    <link href="/static/css/main.css" rel="stylesheet">
    
    <style>
        /* 现代化基础样式 */
        :root {
            --primary-color: #4e73df;
            --primary-gradient: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            --success-color: #1cc88a;
            --warning-color: #f6c23e;
            --danger-color: #e74a3b;
            --info-color: #36b9cc;
            --dark-color: #5a5c69;
            --light-bg: #f8f9fc;
            --white-bg: #ffffff;
            --border-color: #e3e6f0;
            --text-primary: #5a5c69;
            --text-muted: #858796;
            --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
            --border-radius: 0.5rem;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 全局样式重置 */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
            background: var(--light-bg);
            color: var(--text-primary);
            line-height: 1.6;
            font-size: 14px;
            margin: 0;
            padding: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 顶部导航栏样式 */
        .navbar {
            background: var(--primary-gradient) !important;
            box-shadow: var(--shadow-md);
            border: none;
            padding: 0.75rem 1rem;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.25rem;
            color: #ffffff !important;
            text-decoration: none;
        }

        .navbar-brand:hover {
            color: #f8f9fc !important;
        }

        .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            transition: var(--transition);
        }

        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link.active {
            color: #ffffff !important;
            background: rgba(255, 255, 255, 0.1);
        }

        .dropdown-menu {
            background: var(--white-bg);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-lg);
            border-radius: var(--border-radius);
        }

        .dropdown-item {
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            transition: var(--transition);
        }

        .dropdown-item:hover {
            background: var(--light-bg);
            color: var(--text-primary);
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            top: 76px;
            left: 0;
            width: 260px;
            height: calc(100vh - 76px);
            background: var(--primary-gradient);
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-md);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            border-radius: 0;
            transition: var(--transition);
            border-left: 3px solid transparent;
        }

        .sidebar .nav-link:hover {
            color: #ffffff;
            background: rgba(255, 255, 255, 0.1);
            border-left-color: rgba(255, 255, 255, 0.5);
        }

        .sidebar .nav-link.active {
            color: #ffffff;
            background: rgba(255, 255, 255, 0.15);
            border-left-color: #ffffff;
            font-weight: 600;
        }

        .sidebar .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
            text-align: center;
        }

        .sidebar-heading {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 1rem 1.5rem 0.5rem;
            margin-top: 1rem;
        }

        .sidebar-heading:first-child {
            margin-top: 0;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 260px;
            min-height: calc(100vh - 76px);
            background: var(--white-bg);
            padding: 2rem;
        }

        /* 面包屑导航 */
        .breadcrumb {
            background: transparent;
            padding: 0;
            margin-bottom: 1.5rem;
        }

        .breadcrumb-item {
            color: var(--text-muted);
        }

        .breadcrumb-item.active {
            color: var(--text-primary);
            font-weight: 500;
        }

        .breadcrumb-item a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .breadcrumb-item a:hover {
            text-decoration: underline;
        }

        /* 页面标题区域 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 1.5rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid var(--border-color);
        }

        .page-header h1 {
            color: var(--text-primary);
            font-size: 1.75rem;
            font-weight: 600;
            margin: 0;
        }

        .page-actions .btn {
            margin-left: 0.5rem;
        }

        /* 消息提示样式 */
        .alert {
            border: none;
            border-radius: var(--border-radius);
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow-sm);
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid var(--success-color);
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid var(--danger-color);
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left: 4px solid var(--info-color);
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left: 4px solid var(--warning-color);
        }

        /* 卡片样式 */
        .card {
            background: var(--white-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
        }

        .card:hover {
            box-shadow: var(--shadow-md);
        }

        .card-header {
            background: var(--light-bg);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .card-body {
            padding: 1.25rem;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 1rem;
            }

            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .page-actions {
                width: 100%;
            }

            .page-actions .btn {
                margin: 0 0.25rem 0.5rem 0;
            }
        }

        /* 页脚样式 */
        .footer {
            background: var(--white-bg);
            border-top: 1px solid var(--border-color);
            padding: 1.5rem 0;
            margin-top: 3rem;
            color: var(--text-muted);
        }
    </style>
    
    {{block "head" .}}{{end}}
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-alt"></i>
                蜜罐管理平台
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {{if eq .ActivePage "dashboard"}}active{{end}}" href="/dashboard">
                            <i class="fas fa-tachometer-alt"></i> 仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{if eq .ActivePage "nodes"}}active{{end}}" href="/nodes">
                            <i class="fas fa-server"></i> 节点管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{if eq .ActivePage "templates"}}active{{end}}" href="/templates">
                            <i class="fas fa-cube"></i> 模板管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{if eq .ActivePage "attacks"}}active{{end}}" href="/attacks">
                            <i class="fas fa-exclamation-triangle"></i> 攻击数据
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {{if .User}}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{.User.Username}}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/profile"><i class="fas fa-user-cog"></i> 个人设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                        </ul>
                    </li>
                    {{else}}
                    <li class="nav-item">
                        <a class="nav-link" href="/login">
                            <i class="fas fa-sign-in-alt"></i> 登录
                        </a>
                    </li>
                    {{end}}
                </ul>
            </div>
        </div>
    </nav>

    <!-- 侧边栏 -->
    <nav class="sidebar">
        <div class="pt-3">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "dashboard"}}active{{end}}" href="/dashboard">
                        <i class="fas fa-home"></i> 概览
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "realtime"}}active{{end}}" href="/realtime">
                        <i class="fas fa-eye"></i> 实时监控
                    </a>
                </li>
            </ul>

            <h6 class="sidebar-heading">节点管理</h6>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "nodes"}}active{{end}}" href="/nodes">
                        <i class="fas fa-server"></i> 节点列表
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "node-status"}}active{{end}}" href="/nodes/status">
                        <i class="fas fa-heartbeat"></i> 状态监控
                    </a>
                </li>
            </ul>

            <h6 class="sidebar-heading">服务管理</h6>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "templates"}}active{{end}}" href="/templates">
                        <i class="fas fa-cube"></i> 模板管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "deployments"}}active{{end}}" href="/deployments">
                        <i class="fas fa-rocket"></i> 部署管理
                    </a>
                </li>
            </ul>

            <h6 class="sidebar-heading">数据分析</h6>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "attacks"}}active{{end}}" href="/attacks">
                        <i class="fas fa-bug"></i> 攻击数据
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "statistics"}}active{{end}}" href="/statistics">
                        <i class="fas fa-chart-line"></i> 统计分析
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "reports"}}active{{end}}" href="/reports">
                        <i class="fas fa-file-alt"></i> 报表生成
                    </a>
                </li>
            </ul>

            {{if and .User (eq .User.Role "administrator")}}
            <h6 class="sidebar-heading">系统管理</h6>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "users"}}active{{end}}" href="/users">
                        <i class="fas fa-users"></i> 用户管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "settings"}}active{{end}}" href="/settings">
                        <i class="fas fa-cog"></i> 系统设置
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "logs"}}active{{end}}" href="/logs">
                        <i class="fas fa-list"></i> 系统日志
                    </a>
                </li>
            </ul>
            {{end}}
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 面包屑导航 -->
        {{if .Breadcrumb}}
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                {{range .Breadcrumb}}
                <li class="breadcrumb-item {{if .Active}}active{{end}}">
                    {{if .URL}}<a href="{{.URL}}">{{.Name}}</a>{{else}}{{.Name}}{{end}}
                </li>
                {{end}}
            </ol>
        </nav>
        {{end}}

        <!-- 页面标题 -->
        {{if .PageHeader}}
        <div class="page-header">
            <h1>{{.PageHeader}}</h1>
            {{if .PageActions}}
            <div class="page-actions">
                {{range .PageActions}}
                <a href="{{.URL}}" class="btn {{.Class}}">
                    {{if .Icon}}<i class="{{.Icon}}"></i>{{end}} {{.Text}}
                </a>
                {{end}}
            </div>
            {{end}}
        </div>
        {{end}}

        <!-- 消息提示 -->
        <div id="flash-messages">
            {{if .SuccessMessage}}
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i> {{.SuccessMessage}}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {{end}}
            
            {{if .ErrorMessage}}
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle"></i> {{.ErrorMessage}}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {{end}}
            
            {{if .InfoMessage}}
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle"></i> {{.InfoMessage}}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {{end}}
        </div>

        <!-- 页面内容 -->
        {{block "content" .}}{{end}}
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <span>© 2024 蜜罐管理平台. All rights reserved.</span>
                </div>
                <div class="col-md-6 text-end">
                    <span>Version 1.0.0</span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="/static/libs/bootstrap/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/main.js"></script>
    
    {{block "scripts" .}}{{end}}
</body>
</html>
