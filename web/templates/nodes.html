<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>节点管理 - 蜜罐管理平台</title>
    <link href="/static/libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="/static/libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/dashboard.css" rel="stylesheet">
    <style>
        /* 现代简约设计系统 */
        :root {
            /* 简约配色方案 */
            --minimal-white: #ffffff;
            --minimal-gray-50: #fafafa;
            --minimal-gray-100: #f5f5f5;
            --minimal-gray-200: #eeeeee;
            --minimal-gray-300: #e0e0e0;
            --minimal-gray-400: #bdbdbd;
            --minimal-gray-500: #9e9e9e;
            --minimal-gray-600: #757575;
            --minimal-gray-700: #616161;
            --minimal-gray-800: #424242;
            --minimal-gray-900: #212121;
            
            /* 功能色彩 - 简约版本 */
            --minimal-primary: #1976d2;
            --minimal-success: #388e3c;
            --minimal-warning: #f57c00;
            --minimal-danger: #d32f2f;
            --minimal-info: #0288d1;
            
            /* 文字颜色 */
            --minimal-text-primary: #212121;
            --minimal-text-secondary: #757575;
            --minimal-text-disabled: #bdbdbd;
            
            /* 阴影系统 - 极简版本 */
            --minimal-shadow-1: 0 1px 3px rgba(0, 0, 0, 0.12);
            --minimal-shadow-2: 0 2px 6px rgba(0, 0, 0, 0.12);
            --minimal-shadow-3: 0 4px 12px rgba(0, 0, 0, 0.15);
            --minimal-shadow-4: 0 8px 24px rgba(0, 0, 0, 0.15);
            
            /* 圆角系统 */
            --minimal-radius-xs: 2px;
            --minimal-radius-sm: 4px;
            --minimal-radius-md: 6px;
            --minimal-radius-lg: 8px;
            --minimal-radius-xl: 12px;
            
            /* 间距系统 */
            --minimal-space-xs: 4px;
            --minimal-space-sm: 8px;
            --minimal-space-md: 16px;
            --minimal-space-lg: 24px;
            --minimal-space-xl: 32px;
            --minimal-space-2xl: 48px;
            
            /* 过渡动画 */
            --minimal-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            
            /* 边框 */
            --minimal-border: 1px solid var(--minimal-gray-200);
            --minimal-border-hover: 1px solid var(--minimal-gray-300);
        }

        /* 侧边栏导航样式 */
        .main-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 260px;
            height: 100vh;
            background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar-brand {
            padding: 1.5rem 1.25rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-brand a {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.125rem;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-section {
            margin-bottom: 1.5rem;
        }

        .nav-section-title {
            padding: 0.5rem 1.25rem;
            font-size: 0.75rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.6);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.5rem;
        }

        .nav-item {
            margin: 0 0.75rem;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(4px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .nav-link i {
            width: 20px;
            text-align: center;
            font-size: 1rem;
        }

        .sidebar-user {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 1rem 1.25rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(0, 0, 0, 0.2);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: white;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
        }

        .user-info:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .user-avatar {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            font-size: 0.875rem;
            line-height: 1.2;
        }

        .user-role {
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.7);
        }

        /* 主内容区域调整 - Hfish风格 */
        .main-content {
            margin-left: 260px;
            min-height: 100vh;
            background: #f8f9fa;
            padding: 0;
        }

        /* Hfish风格的容器 */
        .container-fluid {
            background: #f8f9fa;
            padding: 2rem 1.5rem;
        }

        /* Hfish风格的页面头部 - 提高优先级 */
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            padding: 2rem 0 !important;
            margin: -2rem -1.5rem 2rem -1.5rem !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important;
            border: none !important;
            border-radius: 0 !important;
        }

        .page-header h2 {
            color: white !important;
            font-weight: 300 !important;
            font-size: 2rem !important;
            margin-bottom: 0.5rem !important;
        }

        .page-header p {
            color: rgba(255,255,255,0.9) !important;
            font-size: 1.1rem !important;
            margin-bottom: 0 !important;
        }

        /* Hfish风格的卡片 */
        .card {
            background: #ffffff;
            border: none;
            border-radius: 1rem;
            box-shadow: 0 2px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid #dee2e6;
            color: #495057;
            font-weight: 600;
            padding: 1.25rem 1.5rem;
        }

        .card-body {
            background: #ffffff;
            color: #495057;
            padding: 1.5rem;
        }

        /* Hfish风格的统计卡片 */
        .border-left-primary {
            border-left: 4px solid #667eea !important;
        }

        .border-left-success {
            border-left: 4px solid #28a745 !important;
        }

        .border-left-info {
            border-left: 4px solid #17a2b8 !important;
        }

        .border-left-warning {
            border-left: 4px solid #ffc107 !important;
        }

        /* Hfish风格的统计卡片悬停效果 */
        .border-left-primary:hover,
        .border-left-success:hover,
        .border-left-info:hover,
        .border-left-warning:hover {
            background: #ffffff !important;
        }

        .border-left-primary:hover .card-body,
        .border-left-success:hover .card-body,
        .border-left-info:hover .card-body,
        .border-left-warning:hover .card-body {
            background: #ffffff !important;
        }

        /* Hfish风格的文字颜色 */
        .text-primary {
            color: #667eea !important;
        }

        .text-success {
            color: #28a745 !important;
        }

        .text-info {
            color: #17a2b8 !important;
        }

        .text-warning {
            color: #ffc107 !important;
        }

        .text-gray-800 {
            color: #343a40 !important;
            font-weight: 600;
        }

        .text-muted {
            color: #6c757d !important;
        }

        .text-xs {
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Hfish风格的表格 */
        .table {
            background: #ffffff;
            color: #000000;
            border-radius: 0.75rem;
            overflow: hidden;
            border: none;
        }

        .table thead {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .table thead th {
            color: white;
            font-weight: 600;
            border: none;
            padding: 1rem 1.25rem;
        }

        .table tbody td {
            border: none;
            padding: 1rem 1.25rem;
            border-bottom: 1px solid #f1f3f4;
            background: #ffffff;
            color: #000000;
        }

        .table tbody tr {
            background: #ffffff;
            color: #000000;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .table tbody tr:hover {
            background: #ffffff;
            color: #000000;
        }

        .table tbody tr:hover td {
            background: #ffffff;
            color: #000000;
        }

        /* Hfish风格的按钮 */
        .btn {
            border-radius: 0.5rem;
            font-weight: 500;
            padding: 0.5rem 1.25rem;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
        }

        .btn-outline-secondary {
            border-color: #6c757d;
            color: #6c757d;
        }

        .btn-outline-secondary:hover {
            background: #6c757d;
            border-color: #6c757d;
            transform: translateY(-1px);
        }

        .btn-outline-primary {
            border-color: #667eea;
            color: #667eea;
        }

        .btn-outline-primary:hover {
            background: #667eea;
            border-color: #667eea;
            transform: translateY(-1px);
        }

        .btn-outline-success {
            border-color: #28a745;
            color: #28a745;
        }

        .btn-outline-success:hover {
            background: #28a745;
            border-color: #28a745;
            transform: translateY(-1px);
        }

        .btn-outline-danger {
            border-color: #dc3545;
            color: #dc3545;
        }

        .btn-outline-danger:hover {
            background: #dc3545;
            border-color: #dc3545;
            transform: translateY(-1px);
        }

        /* Hfish风格的徽章 */
        .badge {
            border-radius: 0.375rem;
            font-weight: 500;
            padding: 0.375rem 0.75rem;
        }

        .bg-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
        }

        .bg-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%) !important;
        }

        .bg-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
        }

        .bg-info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
        }

        .bg-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
        }

        /* Hfish风格的表单控件 */
        .form-control, .form-select {
            border-radius: 0.5rem;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        /* Hfish风格的输入组 */
        .input-group-text {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem 0 0 0.5rem;
        }

        /* Hfish风格的分页 */
        .pagination .page-link {
            border-radius: 0.375rem;
            margin: 0 0.125rem;
            border: 1px solid #dee2e6;
            color: #667eea;
        }

        .pagination .page-link:hover {
            background: #667eea;
            border-color: #667eea;
            color: white;
        }

        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
        }

        /* Hfish风格的图标 */
        .text-gray-300 {
            color: #dee2e6 !important;
            opacity: 0.7;
        }

        /* Hfish风格的阴影效果 */
        .shadow {
            box-shadow: 0 2px 20px rgba(0,0,0,0.08) !important;
        }

        /* Hfish风格的间距调整 */
        .py-2 {
            padding-top: 1rem !important;
            padding-bottom: 1rem !important;
        }

        .py-3 {
            padding-top: 1.25rem !important;
            padding-bottom: 1.25rem !important;
        }

        .mb-4 {
            margin-bottom: 2rem !important;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .page-header {
                margin: -2rem -1rem 1.5rem -1rem;
                padding: 1.5rem 0;
            }
            
            .page-header h2 {
                font-size: 1.5rem;
            }
            
            .container-fluid {
                padding: 1.5rem 1rem;
            }
        }

        /* 移动端适配 */
        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1001;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 0.5rem;
            padding: 0.5rem;
            font-size: 1.25rem;
        }

        @media (max-width: 768px) {
            .main-sidebar {
                transform: translateX(-100%);
            }

            .main-sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar-toggle {
                display: block;
            }
        }

        /* 全局样式重置 */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: var(--minimal-gray-50);
            color: var(--minimal-text-primary);
            line-height: 1.5;
            margin: 0;
            padding: 0;
            font-size: 14px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 保持原有的简约页面标题样式 */
        .page-header {
            background: var(--minimal-white);
            border: var(--minimal-border);
            border-radius: var(--minimal-radius-lg);
            padding: var(--minimal-space-xl);
            margin-bottom: var(--minimal-space-xl);
            box-shadow: var(--minimal-shadow-1);
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--minimal-text-primary);
            margin-bottom: var(--minimal-space-sm);
            letter-spacing: -0.02em;
        }

        .page-subtitle {
            color: var(--minimal-text-secondary);
            font-size: 16px;
            font-weight: 400;
            margin: 0;
        }

        /* 其他原有样式保持不变... */
    </style>
</head>
<body>
    <!-- 移动端侧边栏切换按钮 -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="bi bi-list"></i>
    </button>

    <!-- 左侧导航栏 -->
    <div class="main-sidebar" id="mainSidebar">
        <!-- 品牌标识 -->
        <div class="sidebar-brand">
            <a href="/dashboard">
                <i class="bi bi-shield-check"></i>
                蜜罐管理平台
            </a>
        </div>

        <!-- 导航菜单 -->
        <nav class="sidebar-nav">
            <!-- 主要功能 -->
            <div class="nav-section">
                <div class="nav-section-title">主要功能</div>
                <div class="nav-item">
                    <a class="nav-link" href="/dashboard">
                        <i class="bi bi-speedometer2"></i>
                        <span>仪表板</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link active" href="/nodes">
                        <i class="bi bi-hdd-network"></i>
                        <span>节点管理</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/templates">
                        <i class="bi bi-layers"></i>
                        <span>模板管理</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/deployments">
                        <i class="bi bi-play-circle"></i>
                        <span>部署管理</span>
                    </a>
                </div>
            </div>

            <!-- 数据分析 -->
            <div class="nav-section">
                <div class="nav-section-title">数据分析</div>
                <div class="nav-item">
                    <a class="nav-link" href="/intelligence">
                        <i class="bi bi-shield-exclamation"></i>
                        <span>情报数据</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/statistics">
                        <i class="bi bi-bar-chart"></i>
                        <span>统计分析</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/reports">
                        <i class="bi bi-file-text"></i>
                        <span>报表生成</span>
                    </a>
                </div>
            </div>

            <!-- 系统管理 -->
            <div class="nav-section">
                <div class="nav-section-title">系统管理</div>
                <div class="nav-item">
                    <a class="nav-link" href="/users">
                        <i class="bi bi-people"></i>
                        <span>用户管理</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/settings">
                        <i class="bi bi-gear"></i>
                        <span>系统设置</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/logs">
                        <i class="bi bi-journal-text"></i>
                        <span>系统日志</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- 用户信息 -->
        <div class="sidebar-user">
            <div class="user-info" onclick="toggleUserMenu()">
                <div class="user-avatar">
                    <i class="bi bi-person"></i>
                </div>
                <div class="user-details">
                    <div class="user-name" id="sidebar-username">管理员</div>
                    <div class="user-role">系统管理员</div>
                </div>
                <i class="bi bi-chevron-up"></i>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- Hfish风格的页面头部 -->
            <div class="page-header">
                <div class="row">
                    <div class="col-md-6">
                        <h2>
                            <i class="bi bi-hdd-network me-3"></i>节点管理
                        </h2>
                        <p>管理和监控蜜罐节点</p>
                    </div>
                    <div class="col-md-6 text-end d-flex align-items-center justify-content-end">
                        <button class="btn btn-light me-2" data-bs-toggle="modal" data-bs-target="#addNodeModal">
                            <i class="bi bi-plus-circle me-2"></i>添加节点
                        </button>
                        <button class="btn btn-light" onclick="refreshNodes()">
                            <i class="bi bi-arrow-clockwise me-2"></i>刷新
                        </button>
                    </div>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        总节点数
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-nodes">
                                        <span class="spinner-border spinner-border-sm"></span>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-hdd-network fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        在线节点
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="online-nodes">
                                        <span class="spinner-border spinner-border-sm"></span>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        离线节点
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="offline-nodes">
                                        <span class="spinner-border spinner-border-sm"></span>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-x-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        运行服务
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="running-services">
                                        <span class="spinner-border spinner-border-sm"></span>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-play-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索和过滤 -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" class="form-control" id="search-input" placeholder="搜索节点名称、IP地址...">
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="status-filter">
                        <option value="">所有状态</option>
                        <option value="online">在线</option>
                        <option value="offline">离线</option>
                        <option value="error">错误</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="region-filter">
                        <option value="">所有区域</option>
                        <option value="asia">亚洲</option>
                        <option value="europe">欧洲</option>
                        <option value="america">美洲</option>
                    </select>
                </div>
            </div>

            <!-- 节点列表 -->
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">节点列表</h6>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-three-dots"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="exportNodes()">
                                <i class="bi bi-download me-2"></i>导出数据
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="batchOperation()">
                                <i class="bi bi-check2-square me-2"></i>批量操作
                            </a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="nodes-table">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="select-all">
                                    </th>
                                    <th>节点ID</th>
                                    <th>名称</th>
                                    <th>IP地址</th>
                                    <th>区域</th>
                                    <th>状态</th>
                                    <th>运行服务</th>
                                    <th>最后心跳</th>
                                    <th width="120">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="9" class="text-center">
                                        <span class="spinner-border spinner-border-sm me-2"></span>
                                        加载中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <nav aria-label="节点分页" class="mt-3">
                        <ul class="pagination justify-content-center" id="pagination">
                            <!-- 分页按钮将通过JavaScript生成 -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>

        <!-- 添加节点模态框 -->
        <div class="modal fade" id="addNodeModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-plus-circle me-2"></i>添加节点
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="add-node-form">
                            <div class="mb-3">
                                <label for="node-name" class="form-label">节点名称</label>
                                <input type="text" class="form-control" id="node-name" required>
                            </div>
                            <div class="mb-3">
                                <label for="node-ip" class="form-label">IP地址</label>
                                <input type="text" class="form-control" id="node-ip" required>
                            </div>
                            <div class="mb-3">
                                <label for="node-region" class="form-label">区域</label>
                                <select class="form-select" id="node-region" required>
                                    <option value="">选择区域</option>
                                    <option value="asia">亚洲</option>
                                    <option value="europe">欧洲</option>
                                    <option value="america">美洲</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="node-description" class="form-label">描述</label>
                                <textarea class="form-control" id="node-description" rows="3"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="addNode()">添加节点</button>
                    </div>
                </div>
            </div>
        </div>

    <script src="/static/libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="/static/js/nodes.js"></script>
</body>
</html>
