<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 蜜罐管理平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="/static/libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="/static/libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --primary-light: #7c3aed;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --surface-color: #ffffff;
            --background-color: #f8fafc;
            --background-secondary: #f1f5f9;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #e2e8f0;
            --border-light: #f1f5f9;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            margin: 0;
            line-height: 1.6;
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .login-container {
            width: 100%;
            max-width: 1100px;
            background: var(--surface-color);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            display: grid;
            grid-template-columns: 1.2fr 1fr;
            min-height: 650px;
            position: relative;
            z-index: 1;
        }

        /* 左侧品牌展示区域 */
        .brand-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            padding: 4rem 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .brand-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hexagons" width="28" height="49" patternUnits="userSpaceOnUse" patternTransform="scale(0.8)"><polygon points="14,1 26,7 26,21 14,27 2,21 2,7" fill="none" stroke="rgba(255,255,255,0.15)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23hexagons)"/></svg>');
            opacity: 0.4;
        }

        .brand-content {
            position: relative;
            z-index: 1;
            width: 100%;
        }

        .brand-logo {
            width: 140px;
            height: 140px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 2.5rem;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .brand-logo i {
            font-size: 3.2rem;
            color: white;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .brand-logo {
                width: 70px;
                height: 70px;
                margin-bottom: 2rem;
            }

            .brand-logo i {
                font-size: 1.8rem;
            }
        }

        @media (max-width: 480px) {
            .brand-logo {
                width: 60px;
                height: 60px;
            }

            .brand-logo i {
                font-size: 1.5rem;
            }
        }

        .brand-title {
            font-size: 2.8rem;
            font-weight: 800;
            margin-bottom: 1rem;
            letter-spacing: -0.02em;
            line-height: 1.1;
        }

        .brand-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 400;
            margin-bottom: 3rem;
            line-height: 1.5;
            max-width: 400px;
        }

        .brand-features {
            list-style: none;
            padding: 0;
            margin: 0;
            width: 100%;
        }

        .brand-features li {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            font-size: 1rem;
            opacity: 0.95;
            padding: 0.75rem 0;
        }

        .brand-features li i {
            margin-right: 1rem;
            font-size: 1.1rem;
            width: 24px;
            text-align: center;
            background: rgba(255, 255, 255, 0.15);
            padding: 0.5rem;
            border-radius: var(--radius-sm);
        }

        .offline-badge {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .offline-badge i {
            margin-right: 0.5rem;
        }

        /* 右侧登录区域 */
        .login-section {
            padding: 4rem 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: var(--surface-color);
        }

        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .login-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .login-description {
            font-size: 1rem;
            color: var(--text-secondary);
            margin-bottom: 0;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.75rem;
        }

        .input-wrapper {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
            font-size: 1.1rem;
            z-index: 2;
        }

        .form-control {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--surface-color);
            color: var(--text-primary);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-control::placeholder {
            color: var(--text-muted);
        }

        .form-check-wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 2rem;
        }

        .form-check {
            display: flex;
            align-items: center;
        }

        .form-check-input {
            width: 1.1rem;
            height: 1.1rem;
            margin-right: 0.75rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-sm);
            cursor: pointer;
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .form-check-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            cursor: pointer;
        }

        .forgot-link {
            font-size: 0.9rem;
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .forgot-link:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        .btn-login {
            width: 100%;
            padding: 1rem 1.5rem;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            margin-bottom: 2rem;
        }

        .btn-login:hover:not(:disabled) {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-login:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .system-info {
            background: var(--background-secondary);
            border-radius: var(--radius-md);
            padding: 1.25rem;
            border: 1px solid var(--border-light);
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            font-size: 0.85rem;
        }

        .info-item {
            display: flex;
            align-items: center;
            color: var(--text-secondary);
        }

        .info-item i {
            margin-right: 0.75rem;
            width: 16px;
            text-align: center;
            font-size: 0.9rem;
        }

        .status-online {
            color: var(--success-color);
            font-weight: 600;
        }

        .status-offline {
            color: var(--warning-color);
            font-weight: 600;
        }

        .alert {
            padding: 1rem;
            border-radius: var(--radius-md);
            margin-bottom: 1.5rem;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .alert-danger {
            background: #fef2f2;
            color: var(--danger-color);
            border: 1px solid #fecaca;
        }

        .alert-success {
            background: #f0fdf4;
            color: var(--success-color);
            border: 1px solid #bbf7d0;
        }

        .alert-info {
            background: #eff6ff;
            color: var(--primary-color);
            border: 1px solid #bfdbfe;
        }

        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }

        /* 响应式设计 */
        @media (max-width: 992px) {
            .login-container {
                grid-template-columns: 1fr;
                max-width: 500px;
            }

            .brand-section {
                padding: 3rem 2rem;
                min-height: 350px;
                align-items: center;
                text-align: center;
            }

            .brand-content {
                text-align: center;
            }

            .brand-logo {
                margin: 0 auto 2rem;
            }

            .brand-features {
                display: none;
            }

            .offline-badge {
                position: static;
                margin-top: 2rem;
                display: inline-block;
            }

            .login-section {
                padding: 3rem 2rem;
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }

            .brand-section {
                padding: 2rem 1.5rem;
                min-height: 300px;
            }

            .brand-title {
                font-size: 2rem;
            }

            .brand-subtitle {
                font-size: 1rem;
                margin-bottom: 2rem;
            }

            .login-section {
                padding: 2rem 1.5rem;
            }

            .login-title {
                font-size: 1.75rem;
            }

            .info-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 0.5rem;
            }

            .brand-section {
                padding: 1.5rem;
                min-height: 250px;
            }

            .login-section {
                padding: 1.5rem;
            }

            .brand-title {
                font-size: 1.75rem;
            }

            .login-title {
                font-size: 1.5rem;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-15px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-container {
            animation: fadeInUp 0.7s ease-out;
        }

        .brand-section {
            animation: slideInLeft 0.9s ease-out;
        }

        .login-section {
            animation: slideInRight 0.9s ease-out;
        }

        .alert {
            animation: slideDown 0.4s ease-out;
        }

        /* 输入框聚焦动画 */
        .input-wrapper:focus-within .input-icon {
            color: var(--primary-color);
            transform: translateY(-50%) scale(1.1);
        }

        /* 加载状态样式 */
        .btn-login.loading {
            pointer-events: none;
        }

        /* 深色模式支持 */
        @media (prefers-color-scheme: dark) {
            :root {
                --surface-color: #1e293b;
                --background-color: #0f172a;
                --background-secondary: #334155;
                --text-primary: #f1f5f9;
                --text-secondary: #cbd5e1;
                --text-muted: #94a3b8;
                --border-color: #334155;
                --border-light: #475569;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- 左侧品牌展示区域 -->
        <div class="brand-section">
            <div class="offline-badge">
                <i class="bi bi-wifi-off"></i>
                离线模式
            </div>
            
            <div class="brand-content">
                <div class="brand-logo">
                    <i class="bi bi-shield-check"></i>
                </div>
                <h1 class="brand-title">蜜罐管理平台</h1>
                <p class="brand-subtitle">专业的网络安全防护与威胁情报收集系统，为您的网络安全保驾护航</p>
                
                <ul class="brand-features">
                    <li>
                        实时威胁检测与智能防护
                    </li>
                    <li>
                        深度数据分析与可视化报告
                    </li>
                    <li>
                        分布式集群部署管理
                    </li>
                    <li>
                        灵活配置与模块化扩展
                    </li>
                </ul>
            </div>
        </div>

        <!-- 右侧登录区域 -->
        <div class="login-section">
            <div class="login-header">
                <h2 class="login-title">欢迎回来</h2>
                <p class="login-description">请登录您的管理员账户以继续使用系统</p>
            </div>

            <!-- 错误和信息提示 -->
            {{if .ErrorMessage}}
            <div class="alert alert-danger" role="alert">
                <i class="bi bi-exclamation-triangle-fill"></i>
                {{.ErrorMessage}}
            </div>
            {{end}}
            
            {{if .InfoMessage}}
            <div class="alert alert-info" role="alert">
                <i class="bi bi-info-circle-fill"></i>
                {{.InfoMessage}}
            </div>
            {{end}}

            <!-- 登录表单 -->
            <form id="loginForm" method="POST" action="/login">
                <div class="form-group">
                    <label for="username" class="form-label">用户名</label>
                    <div class="input-wrapper">
                        <i class="input-icon bi bi-person"></i>
                        <input type="text" 
                               class="form-control" 
                               id="username" 
                               name="username" 
                               placeholder="请输入管理员用户名" 
                               required 
                               autocomplete="username">
                    </div>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">密码</label>
                    <div class="input-wrapper">
                        <i class="input-icon bi bi-lock"></i>
                        <input type="password" 
                               class="form-control" 
                               id="password" 
                               name="password" 
                               placeholder="请输入登录密码" 
                               required 
                               autocomplete="current-password">
                    </div>
                </div>

                <div class="form-check-wrapper">
                    <div class="form-check">
                        <input class="form-check-input" 
                               type="checkbox" 
                               id="remember" 
                               name="remember">
                        <label class="form-check-label" for="remember">
                            记住登录状态
                        </label>
                    </div>
                    <a href="/forgot-password" class="forgot-link">忘记密码？</a>
                </div>

                <button type="submit" class="btn-login">
                    <i class="bi bi-box-arrow-in-right"></i>
                    <span>登录系统</span>
                </button>
            </form>

            <!-- 系统信息 -->
            <div class="system-info">
                <div class="info-grid">
                    <div class="info-item">
                        <i class="bi bi-server"></i>
                        系统状态: <span class="status-online">正常运行</span>
                    </div>
                    <div class="info-item">
                        <i class="bi bi-clock"></i>
                        <span id="current-time">{{.CurrentTime}}</span>
                    </div>
                    <div class="info-item">
                        <i class="bi bi-shield-check"></i>
                        安全连接: <span class="status-online">已启用</span>
                    </div>
                    <div class="info-item">
                        <i class="bi bi-wifi-off"></i>
                        网络模式: <span class="status-offline">离线模式</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/static/libs/bootstrap/bootstrap.bundle.min.js"></script>
    
    <script>
        // 登录表单处理
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const remember = document.getElementById('remember').checked;
            
            if (!username || !password) {
                showAlert('请输入用户名和密码', 'danger');
                return;
            }
            
            // 显示加载状态
            const submitBtn = this.querySelector('button[type="submit"]');
            const btnText = submitBtn.querySelector('span');
            const btnIcon = submitBtn.querySelector('i');
            const originalText = btnText.textContent;
            
            btnIcon.className = 'spinner-border spinner-border-sm';
            btnText.textContent = '登录中...';
            submitBtn.disabled = true;
            submitBtn.classList.add('loading');
            
            // 发送登录请求
            fetch('/api/v1/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    password: password,
                    remember: remember
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 保存Token
                    localStorage.setItem('token', data.data.token);
                    localStorage.setItem('user', JSON.stringify(data.data.user_info));
                    
                    showAlert('登录成功，正在跳转到管理面板...', 'success');
                    
                    // 跳转到仪表板
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 1500);
                } else {
                    showAlert(data.message || '登录失败，请检查用户名和密码', 'danger');
                }
            })
            .catch(error => {
                console.error('Login error:', error);
                showAlert('网络连接失败，请检查网络连接后重试', 'danger');
            })
            .finally(() => {
                // 恢复按钮状态
                btnIcon.className = 'bi bi-box-arrow-in-right';
                btnText.textContent = originalText;
                submitBtn.disabled = false;
                submitBtn.classList.remove('loading');
            });
        });
        
        // 显示提示信息
        function showAlert(message, type) {
            // 移除现有的提示
            const existingAlerts = document.querySelectorAll('.alert:not([role])');
            existingAlerts.forEach(alert => alert.remove());
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            
            const iconClass = type === 'success' ? 'bi-check-circle-fill' : 
                             type === 'danger' ? 'bi-exclamation-triangle-fill' : 
                             'bi-info-circle-fill';
            
            alertDiv.innerHTML = `
                <i class="bi ${iconClass}"></i>
                ${message}
            `;
            
            // 插入新提示
            const form = document.getElementById('loginForm');
            form.parentNode.insertBefore(alertDiv, form);
            
            // 自动隐藏成功提示
            if (type === 'success') {
                setTimeout(() => {
                    alertDiv.remove();
                }, 4000);
            }
        }
        
        // 检查是否已登录（开发模式下跳过）
        const isDevelopment = window.location.hostname === 'localhost' || 
                             window.location.hostname === '127.0.0.1' ||
                             window.location.search.includes('debug=1');
        
        if (!isDevelopment && localStorage.getItem('token')) {
            // 验证token有效性
            fetch('/api/v1/auth/profile', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            })
            .then(response => {
                if (response.ok) {
                    window.location.href = '/dashboard';
                } else {
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                }
            })
            .catch(() => {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
            });
        }
        
        // 页面加载完成后聚焦用户名输入框
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });
        
        // 回车键快捷登录
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const activeElement = document.activeElement;
                if (activeElement.tagName === 'INPUT') {
                    document.getElementById('loginForm').dispatchEvent(new Event('submit'));
                }
            }
        });

        // 输入验证
        document.getElementById('username').addEventListener('input', function(e) {
            const value = e.target.value.trim();
            if (value.length > 0 && value.length < 3) {
                e.target.setCustomValidity('用户名至少需要3个字符');
            } else {
                e.target.setCustomValidity('');
            }
        });

        document.getElementById('password').addEventListener('input', function(e) {
            const value = e.target.value;
            if (value.length > 0 && value.length < 6) {
                e.target.setCustomValidity('密码至少需要6个字符');
            } else {
                e.target.setCustomValidity('');
            }
        });

        // 实时时间更新（离线模式）
        function updateTime() {
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                const now = new Date();
                const timeString = now.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
                timeElement.textContent = timeString;
            }
        }

        // 每秒更新一次时间
        setInterval(updateTime, 1000);
        updateTime(); // 立即更新一次

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            // ESC键清除错误信息
            if (e.key === 'Escape') {
                const alerts = document.querySelectorAll('.alert:not([role])');
                alerts.forEach(alert => alert.remove());
            }
            
            // Ctrl+Enter 快速登录
            if (e.ctrlKey && e.key === 'Enter') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });

        // 离线状态检测
        function updateNetworkStatus() {
            const offlineBadge = document.querySelector('.offline-badge');
            const networkStatus = document.querySelector('.info-item:last-child span');
            
            if (navigator.onLine) {
                if (offlineBadge) {
                    offlineBadge.innerHTML = '<i class="bi bi-wifi"></i>在线模式';
                }
                if (networkStatus) {
                    networkStatus.innerHTML = '<span class="status-online">在线模式</span>';
                }
            } else {
                if (offlineBadge) {
                    offlineBadge.innerHTML = '<i class="bi bi-wifi-off"></i>离线模式';
                }
                if (networkStatus) {
                    networkStatus.innerHTML = '<span class="status-offline">离线模式</span>';
                }
            }
        }

        // 监听网络状态变化
        window.addEventListener('online', updateNetworkStatus);
        window.addEventListener('offline', updateNetworkStatus);
        
        // 初始化网络状态
        updateNetworkStatus();
    </script>
</body>
</html>
