<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模板管理 - 蜜罐管理平台</title>
    <link href="/static/libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="/static/libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/dashboard.css" rel="stylesheet">
    <style>
        /* 侧边栏导航样式 */
        .main-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 260px;
            height: 100vh;
            background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar-brand {
            padding: 1.5rem 1.25rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-brand a {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.125rem;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-section {
            margin-bottom: 1.5rem;
        }

        .nav-section-title {
            padding: 0.5rem 1.25rem;
            font-size: 0.75rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.6);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.5rem;
        }

        .nav-item {
            margin: 0 0.75rem;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(4px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .nav-link i {
            width: 20px;
            text-align: center;
            font-size: 1rem;
        }

        .sidebar-user {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 1rem 1.25rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(0, 0, 0, 0.2);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: white;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
        }

        .user-info:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .user-avatar {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            font-size: 0.875rem;
            line-height: 1.2;
        }

        .user-role {
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.7);
        }

        /* 主内容区域调整为白色 */
        .main-content {
            margin-left: 260px;
            min-height: 100vh;
            background: #ffffff;
        }

        /* 容器背景调整 */
        .container-fluid {
            background: #ffffff;
        }

        /* 页面标题区域调整 */
        .main-content h2 {
            color: #5a5c69 !important;
        }

        .main-content .text-muted {
            color: #858796 !important;
        }

        /* 统计卡片样式调整为白色主题 */
        .card {
            background: #ffffff;
            border: 1px solid #e3e6f0;
            color: #5a5c69;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }

        .card-body {
            background: #ffffff;
            color: #5a5c69;
        }

        .card .text-gray-800 {
            color: #5a5c69 !important;
        }

        .card .text-gray-300 {
            color: #dddfeb !important;
        }

        /* 模板列表卡片调整 */
        .card.shadow {
            background: #ffffff;
            border: 1px solid #e3e6f0;
        }

        .card.shadow .card-header {
            background: #f8f9fc;
            border-bottom: 1px solid #e3e6f0;
            color: #5a5c69;
        }

        .card.shadow .card-body {
            background: #ffffff;
            color: #5a5c69;
        }

        /* 表格样式调整 */
        .table {
            background: #ffffff;
            color: #5a5c69;
        }

        .table thead th {
            background: #f8f9fc;
            color: #5a5c69;
            border-bottom: 1px solid #e3e6f0;
        }

        .table tbody td {
            background: #ffffff;
            color: #5a5c69;
            border-bottom: 1px solid #e3e6f0;
        }

        .table tbody tr:nth-child(even) td {
            background: #f8f9fc;
        }

        .table tbody tr:hover td {
            background: #eaecf4;
            color: #5a5c69;
        }

        /* 搜索和过滤区域调整 */
        .form-control {
            background: #ffffff;
            border: 1px solid #d1d3e2;
            color: #5a5c69;
        }

        .form-control:focus {
            background: #ffffff;
            border-color: #bac8f3;
            color: #5a5c69;
        }

        .form-select {
            background: #ffffff;
            border: 1px solid #d1d3e2;
            color: #5a5c69;
        }

        .input-group-text {
            background: #f8f9fc;
            border: 1px solid #d1d3e2;
            color: #5a5c69;
        }

        /* 模板网格视图调整 */
        #templates-grid {
            background: #ffffff;
        }

        #templates-grid .col-12 {
            color: #5a5c69;
        }

        /* 模板列表视图调整 */
        #templates-list {
            background: #ffffff;
        }

        /* 分页样式调整 */
        .pagination .page-link {
            background: #ffffff;
            border: 1px solid #d1d3e2;
            color: #5a5c69;
        }

        .pagination .page-link:hover {
            background: #eaecf4;
            border-color: #d1d3e2;
            color: #5a5c69;
        }

        .pagination .page-item.active .page-link {
            background: #4e73df;
            border-color: #4e73df;
            color: #ffffff;
        }

        /* 模态框样式调整 */
        .modal-content {
            background: #ffffff;
            color: #5a5c69;
        }

        .modal-header {
            background: #f8f9fc;
            border-bottom: 1px solid #e3e6f0;
            color: #5a5c69;
        }

        .modal-body {
            background: #ffffff;
            color: #5a5c69;
        }

        .modal-footer {
            background: #f8f9fc;
            border-top: 1px solid #e3e6f0;
        }

        /* 视图切换按钮调整 */
        .btn-group .btn-outline-secondary {
            background: #ffffff;
            border-color: #d1d3e2;
            color: #5a5c69;
        }

        .btn-group .btn-outline-secondary:hover {
            background: #f8f9fc;
            border-color: #d1d3e2;
            color: #5a5c69;
        }

        .btn-group .btn-check:checked + .btn-outline-secondary {
            background: #4e73df;
            border-color: #4e73df;
            color: #ffffff;
        }

        /* 下拉菜单调整 */
        .dropdown-menu {
            background: #ffffff;
            border: 1px solid #e3e6f0;
        }

        .dropdown-item {
            color: #5a5c69;
        }

        .dropdown-item:hover {
            background: #f8f9fc;
            color: #5a5c69;
        }

        /* 移动端适配 */
        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1001;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 0.5rem;
            padding: 0.5rem;
            font-size: 1.25rem;
        }

        @media (max-width: 768px) {
            .main-sidebar {
                transform: translateX(-100%);
            }

            .main-sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar-toggle {
                display: block;
            }
        }
    </style>
</head>
<body>
    <!-- 移动端侧边栏切换按钮 -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="bi bi-list"></i>
    </button>

    <!-- 左侧导航栏 -->
    <div class="main-sidebar" id="mainSidebar">
        <!-- 品牌标识 -->
        <div class="sidebar-brand">
            <a href="/dashboard">
                <i class="bi bi-shield-check"></i>
                蜜罐管理平台
            </a>
        </div>

        <!-- 导航菜单 -->
        <nav class="sidebar-nav">
            <!-- 主要功能 -->
            <div class="nav-section">
                <div class="nav-section-title">主要功能</div>
                <div class="nav-item">
                    <a class="nav-link" href="/dashboard">
                        <i class="bi bi-speedometer2"></i>
                        <span>仪表板</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/nodes">
                        <i class="bi bi-hdd-network"></i>
                        <span>节点管理</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link active" href="/templates">
                        <i class="bi bi-layers"></i>
                        <span>模板管理</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/deployments">
                        <i class="bi bi-play-circle"></i>
                        <span>部署管理</span>
                    </a>
                </div>
            </div>

            <!-- 数据分析 -->
            <div class="nav-section">
                <div class="nav-section-title">数据分析</div>
                <div class="nav-item">
                    <a class="nav-link" href="/intelligence">
                        <i class="bi bi-shield-exclamation"></i>
                        <span>情报数据</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/statistics">
                        <i class="bi bi-bar-chart"></i>
                        <span>统计分析</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/reports">
                        <i class="bi bi-file-text"></i>
                        <span>报表生成</span>
                    </a>
                </div>
            </div>

            <!-- 系统管理 -->
            <div class="nav-section">
                <div class="nav-section-title">系统管理</div>
                <div class="nav-item">
                    <a class="nav-link" href="/users">
                        <i class="bi bi-people"></i>
                        <span>用户管理</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/settings">
                        <i class="bi bi-gear"></i>
                        <span>系统设置</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/logs">
                        <i class="bi bi-journal-text"></i>
                        <span>系统日志</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- 用户信息 -->
        <div class="sidebar-user">
            <div class="user-info" onclick="toggleUserMenu()">
                <div class="user-avatar">
                    <i class="bi bi-person"></i>
                </div>
                <div class="user-details">
                    <div class="user-name" id="sidebar-username">管理员</div>
                    <div class="user-role">系统管理员</div>
                </div>
                <i class="bi bi-chevron-up"></i>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
        <div class="container-fluid mt-4">
            <!-- 页面标题和操作按钮 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <h2 class="mb-0">
                        <i class="bi bi-layers me-2"></i>模板管理
                    </h2>
                    <p class="text-muted">管理蜜罐部署模板</p>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTemplateModal">
                        <i class="bi bi-plus-circle me-2"></i>添加模板
                    </button>
                    <button class="btn btn-outline-secondary" onclick="refreshTemplates()">
                        <i class="bi bi-arrow-clockwise me-2"></i>刷新
                    </button>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        总模板数
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-templates">
                                        <span class="spinner-border spinner-border-sm"></span>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-layers fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        活跃模板
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-templates">
                                        <span class="spinner-border spinner-border-sm"></span>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        部署次数
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-deployments">
                                        <span class="spinner-border spinner-border-sm"></span>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-play-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        模板类型
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="template-types">
                                        <span class="spinner-border spinner-border-sm"></span>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-collection fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索和过滤 -->
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" class="form-control" id="search-input" placeholder="搜索模板名称、描述...">
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="type-filter">
                        <option value="">所有类型</option>
                        <option value="ssh">SSH</option>
                        <option value="web">Web</option>
                        <option value="ftp">FTP</option>
                        <option value="telnet">Telnet</option>
                        <option value="custom">自定义</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="status-filter">
                        <option value="">所有状态</option>
                        <option value="active">活跃</option>
                        <option value="inactive">非活跃</option>
                        <option value="processing">处理中</option>
                    </select>
                </div>
                <div class="col-md-4 text-end">
                    <div class="btn-group" role="group">
                        <input type="radio" class="btn-check" name="view-mode" id="grid-view" checked>
                        <label class="btn btn-outline-secondary" for="grid-view">
                            <i class="bi bi-grid-3x3-gap"></i>
                        </label>
                        
                        <input type="radio" class="btn-check" name="view-mode" id="list-view">
                        <label class="btn btn-outline-secondary" for="list-view">
                            <i class="bi bi-list"></i>
                        </label>
                    </div>
                </div>
            </div>

            <!-- 模板网格视图 -->
            <div id="templates-grid" class="row">
                <!-- 模板卡片将通过JavaScript生成 -->
                <div class="col-12 text-center">
                    <span class="spinner-border spinner-border-sm me-2"></span>
                    加载中...
                </div>
            </div>

            <!-- 模板列表视图 -->
            <div id="templates-list" class="card shadow d-none">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">模板列表</h6>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-three-dots"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="exportTemplates()">
                                <i class="bi bi-download me-2"></i>导出数据
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="batchOperation()">
                                <i class="bi bi-check2-square me-2"></i>批量操作
                            </a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="templates-table">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="select-all">
                                    </th>
                                    <th>名称</th>
                                    <th>类型</th>
                                    <th>版本</th>
                                    <th>状态</th>
                                    <th>部署次数</th>
                                    <th>创建时间</th>
                                    <th width="120">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="8" class="text-center">
                                        <span class="spinner-border spinner-border-sm me-2"></span>
                                        加载中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <nav aria-label="模板分页" class="mt-3">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- 分页按钮将通过JavaScript生成 -->
                </ul>
            </nav>
        </div>

        <!-- 创建模板模态框 -->
        <div class="modal fade" id="addTemplateModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-plus-circle me-2"></i>创建模板
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="add-template-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="template-name" class="form-label">模板名称</label>
                                        <input type="text" class="form-control" id="template-name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="template-type" class="form-label">模板类型</label>
                                        <select class="form-select" id="template-type" required>
                                            <option value="">选择类型</option>
                                            <option value="ssh">SSH蜜罐</option>
                                            <option value="web">Web蜜罐</option>
                                            <option value="ftp">FTP蜜罐</option>
                                            <option value="telnet">Telnet蜜罐</option>
                                            <option value="custom">自定义</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="template-image" class="form-label">Docker镜像</label>
                                        <input type="text" class="form-control" id="template-image" 
                                               placeholder="例如: honeypot/ssh:latest" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="template-version" class="form-label">版本</label>
                                        <input type="text" class="form-control" id="template-version" 
                                               placeholder="例如: 1.0.0" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="template-description" class="form-label">描述</label>
                                <textarea class="form-control" id="template-description" rows="3" 
                                          placeholder="描述模板的功能和用途"></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="template-config" class="form-label">默认配置 (JSON)</label>
                                <textarea class="form-control" id="template-config" rows="5" 
                                          placeholder='{"port": 22, "banner": "OpenSSH_8.0"}'></textarea>
                                <div class="form-text">请输入有效的JSON格式配置</div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="addTemplate()">创建模板</button>
                    </div>
                </div>
            </div>
        </div>

    <script src="/static/libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="/static/js/templates.js"></script>
</body>
</html>
