<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板 - 蜜罐管理平台</title>
    <link href="/static/libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="/static/libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/dashboard.css" rel="stylesheet">
    <style>
        /* 侧边栏导航样式 */
        .main-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 260px;
            height: 100vh;
            background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar-brand {
            padding: 1.5rem 1.25rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-brand a {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.125rem;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-section {
            margin-bottom: 1.5rem;
        }

        .nav-section-title {
            padding: 0.5rem 1.25rem;
            font-size: 0.75rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.6);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.5rem;
        }

        .nav-item {
            margin: 0 0.75rem;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(4px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .nav-link i {
            width: 20px;
            text-align: center;
            font-size: 1rem;
        }

        .sidebar-user {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 1rem 1.25rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(0, 0, 0, 0.2);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: white;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
        }

        .user-info:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .user-avatar {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            font-size: 0.875rem;
            line-height: 1.2;
        }

        .user-role {
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.7);
        }

        /* 主内容区域调整 - Hfish风格 */
        .main-content {
            margin-left: 260px;
            min-height: 100vh;
            background: #f8f9fa;
            padding: 0;
        }

        /* Hfish风格的容器 */
        .container-fluid {
            background: #f8f9fa;
            padding: 2rem 1.5rem;
        }

        /* Hfish风格的页面头部 */
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin: -2rem -1.5rem 2rem -1.5rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .page-header h2 {
            color: white !important;
            font-weight: 300;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .page-header p {
            color: rgba(255,255,255,0.9) !important;
            font-size: 1.1rem;
            margin-bottom: 0;
        }

        /* Hfish风格的卡片 */
        .card {
            background: #ffffff;
            border: none;
            border-radius: 1rem;
            box-shadow: 0 2px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid #dee2e6;
            color: #495057;
            font-weight: 600;
            padding: 1.25rem 1.5rem;
        }

        .card-body {
            background: #ffffff;
            color: #495057;
            padding: 1.5rem;
        }

        /* Hfish风格的统计卡片 */
        .border-left-primary {
            border-left: 4px solid #667eea !important;
        }

        .border-left-success {
            border-left: 4px solid #28a745 !important;
        }

        .border-left-info {
            border-left: 4px solid #17a2b8 !important;
        }

        .border-left-warning {
            border-left: 4px solid #ffc107 !important;
        }

        /* Hfish风格的统计卡片悬停效果 */
        .border-left-primary:hover,
        .border-left-success:hover,
        .border-left-info:hover,
        .border-left-warning:hover {
            background: #ffffff !important;
        }

        .border-left-primary:hover .card-body,
        .border-left-success:hover .card-body,
        .border-left-info:hover .card-body,
        .border-left-warning:hover .card-body {
            background: #ffffff !important;
        }

        /* Hfish风格的文字颜色 */
        .text-primary {
            color: #667eea !important;
        }

        .text-success {
            color: #28a745 !important;
        }

        .text-info {
            color: #17a2b8 !important;
        }

        .text-warning {
            color: #ffc107 !important;
        }

        .text-gray-800 {
            color: #343a40 !important;
            font-weight: 600;
        }

        .text-muted {
            color: #6c757d !important;
        }

        .text-xs {
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Hfish风格的表格 */
        .table {
            background: #ffffff;
            color: #495057;
            border-radius: 0.75rem;
            overflow: hidden;
            border: none;
        }

        .table thead {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .table thead th {
            color: white;
            font-weight: 600;
            border: none;
            padding: 1rem 1.25rem;
        }

        .table tbody td {
            border: none;
            padding: 1rem 1.25rem;
            border-bottom: 1px solid #f1f3f4;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        /* Hfish风格的进度条 */
        .progress {
            background: #e9ecef;
            border-radius: 0.5rem;
            height: 0.75rem;
        }

        .progress-bar {
            border-radius: 0.5rem;
        }

        .bg-primary .progress-bar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* Hfish风格的按钮 */
        .btn {
            border-radius: 0.5rem;
            font-weight: 500;
            padding: 0.5rem 1.25rem;
            transition: all 0.3s ease;
        }

        .btn-outline-secondary {
            border-color: #6c757d;
            color: #6c757d;
        }

        .btn-outline-secondary:hover {
            background: #6c757d;
            border-color: #6c757d;
            transform: translateY(-1px);
        }

        .btn-outline-primary {
            border-color: #667eea;
            color: #667eea;
        }

        .btn-outline-primary:hover {
            background: #667eea;
            border-color: #667eea;
            transform: translateY(-1px);
        }

        /* Hfish风格的徽章 */
        .badge {
            border-radius: 0.375rem;
            font-weight: 500;
            padding: 0.375rem 0.75rem;
        }

        .bg-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
        }

        .bg-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%) !important;
        }

        .bg-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
        }

        .bg-info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
        }

        .bg-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
        }

        /* Hfish风格的图标 */
        .text-gray-300 {
            color: #dee2e6 !important;
            opacity: 0.7;
        }

        /* Hfish风格的快速操作按钮 */
        .btn-outline-success {
            border-color: #28a745;
            color: #28a745;
        }

        .btn-outline-success:hover {
            background: #28a745;
            border-color: #28a745;
            transform: translateY(-1px);
        }

        .btn-outline-warning {
            border-color: #ffc107;
            color: #ffc107;
        }

        .btn-outline-warning:hover {
            background: #ffc107;
            border-color: #ffc107;
            color: #212529;
            transform: translateY(-1px);
        }

        .btn-outline-danger {
            border-color: #dc3545;
            color: #dc3545;
        }

        .btn-outline-danger:hover {
            background: #dc3545;
            border-color: #dc3545;
            transform: translateY(-1px);
        }

        .btn-outline-info {
            border-color: #17a2b8;
            color: #17a2b8;
        }

        .btn-outline-info:hover {
            background: #17a2b8;
            border-color: #17a2b8;
            transform: translateY(-1px);
        }

        /* Hfish风格的阴影效果 */
        .shadow {
            box-shadow: 0 2px 20px rgba(0,0,0,0.08) !important;
        }

        /* Hfish风格的间距调整 */
        .py-2 {
            padding-top: 1rem !important;
            padding-bottom: 1rem !important;
        }

        .py-3 {
            padding-top: 1.25rem !important;
            padding-bottom: 1.25rem !important;
        }

        .mb-4 {
            margin-bottom: 2rem !important;
        }

        /* 响应式调整保持不变 */
        @media (max-width: 768px) {
            .page-header {
                margin: -2rem -1rem 1.5rem -1rem;
                padding: 1.5rem 0;
            }
            
            .page-header h2 {
                font-size: 1.5rem;
            }
            
            .container-fluid {
                padding: 1.5rem 1rem;
            }
        }

        /* 移动端适配 */
        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1001;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 0.5rem;
            padding: 0.5rem;
            font-size: 1.25rem;
        }

        @media (max-width: 768px) {
            .main-sidebar {
                transform: translateX(-100%);
            }

            .main-sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar-toggle {
                display: block;
            }
        }

        /* 最近活动表格样式调整 */
        #recent-activities-table {
            background: #ffffff;
            color: #5a5c69;
        }

        #recent-activities-table thead th {
            background: #f8f9fc;
            color: #5a5c69;
            border-bottom: 1px solid #e3e6f0;
        }

        #recent-activities-table tbody td {
            background: #ffffff;
            color: #5a5c69;
            border-bottom: 1px solid #e3e6f0;
        }

        #recent-activities-table tbody tr:nth-child(even) td {
            background: #f8f9fc;
        }

        #recent-activities-table tbody tr:hover td {
            background: #eaecf4;
            color: #5a5c69;
        }

        /* 最近活动加载状态调整 */
        #recent-activities .text-center {
            color: #858796;
            background: #ffffff;
        }

        /* 状态徽章颜色保持不变 */
        #recent-activities .badge {
            /* 保持原有的状态颜色 */
        }
    </style>
</head>
<body>
    <!-- 移动端侧边栏切换按钮 -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="bi bi-list"></i>
    </button>

    <!-- 左侧导航栏 -->
    <div class="main-sidebar" id="mainSidebar">
        <!-- 品牌标识 -->
        <div class="sidebar-brand">
            <a href="/dashboard">
                <i class="bi bi-shield-check"></i>
                蜜罐管理平台
            </a>
        </div>

        <!-- 导航菜单 -->
        <nav class="sidebar-nav">
            <!-- 主要功能 -->
            <div class="nav-section">
                <div class="nav-section-title">主要功能</div>
                <div class="nav-item">
                    <a class="nav-link active" href="/dashboard">
                        <i class="bi bi-speedometer2"></i>
                        <span>仪表板</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/nodes">
                        <i class="bi bi-hdd-network"></i>
                        <span>节点管理</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/templates">
                        <i class="bi bi-layers"></i>
                        <span>模板管理</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/deployments">
                        <i class="bi bi-play-circle"></i>
                        <span>部署管理</span>
                    </a>
                </div>
            </div>

            <!-- 数据分析 -->
            <div class="nav-section">
                <div class="nav-section-title">数据分析</div>
                <div class="nav-item">
                    <a class="nav-link" href="/intelligence">
                        <i class="bi bi-shield-exclamation"></i>
                        <span>情报数据</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/statistics">
                        <i class="bi bi-bar-chart"></i>
                        <span>统计分析</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/reports">
                        <i class="bi bi-file-text"></i>
                        <span>报表生成</span>
                    </a>
                </div>
            </div>

            <!-- 系统管理 -->
            <div class="nav-section">
                <div class="nav-section-title">系统管理</div>
                <div class="nav-item">
                    <a class="nav-link" href="/users">
                        <i class="bi bi-people"></i>
                        <span>用户管理</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/settings">
                        <i class="bi bi-gear"></i>
                        <span>系统设置</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="/logs">
                        <i class="bi bi-journal-text"></i>
                        <span>系统日志</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- 用户信息 -->
        <div class="sidebar-user">
            <div class="user-info" onclick="toggleUserMenu()">
                <div class="user-avatar">
                    <i class="bi bi-person"></i>
                </div>
                <div class="user-details">
                    <div class="user-name" id="sidebar-username">管理员</div>
                    <div class="user-role">系统管理员</div>
                </div>
                <i class="bi bi-chevron-up"></i>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- Hfish风格的页面头部 -->
            <div class="page-header">
                <div class="row">
                    <div class="col-md-8">
                        <h2>
                            <i class="bi bi-speedometer2 me-3"></i>系统仪表板
                        </h2>
                        <p>蜜罐系统运行状态概览与实时监控</p>
                    </div>
                    <div class="col-md-4 text-end d-flex align-items-center justify-content-end">
                        <button class="btn btn-light me-2" onclick="refreshDashboard()">
                            <i class="bi bi-arrow-clockwise me-2"></i>刷新数据
                        </button>
                        <button class="btn btn-light" onclick="exportReport()">
                            <i class="bi bi-download me-2"></i>导出报告
                        </button>
                    </div>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        在线节点
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="online-nodes">
                                        <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-hdd-network text-gray-300" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        活跃部署
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-deployments">
                                        <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-play-circle text-gray-300" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        今日攻击
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="today-attacks">
                                        <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-shield-exclamation text-gray-300" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        可用模板
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-templates">
                                        <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-layers text-gray-300" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-graph-up me-2"></i>攻击趋势分析
                            </h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    最近24小时
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="updateChart('24h')">最近24小时</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="updateChart('7d')">最近7天</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="updateChart('30d')">最近30天</a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="card-body">
                            <canvas id="overview-chart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-pie-chart me-2"></i>节点状态分布
                            </h6>
                        </div>
                        <div class="card-body">
                            <canvas id="node-status-chart" width="200" height="200"></canvas>
                            <div class="mt-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="text-sm">在线节点</span>
                                    <span class="badge bg-success" id="online-count">0</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="text-sm">离线节点</span>
                                    <span class="badge bg-danger" id="offline-count">0</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-sm">维护中</span>
                                    <span class="badge bg-warning" id="maintenance-count">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细信息区域 -->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-clock-history me-2"></i>最近活动
                            </h6>
                            <button class="btn btn-sm btn-outline-secondary" onclick="refreshActivities()">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="recent-activities-table">
                                    <thead>
                                        <tr>
                                            <th width="25%">时间</th>
                                            <th width="55%">事件</th>
                                            <th width="20%">状态</th>
                                        </tr>
                                    </thead>
                                    <tbody id="recent-activities">
                                        <tr>
                                            <td colspan="3" class="text-center">
                                                <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-cpu me-2"></i>系统资源监控
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="text-sm font-weight-bold">CPU使用率</span>
                                    <span class="text-sm" id="cpu-percentage">0%</span>
                                </div>
                                <div class="progress mb-3">
                                    <div class="progress-bar bg-primary" role="progressbar" style="width: 0%" id="cpu-usage"></div>
                                </div>
                            </div>
                        
                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="text-sm font-weight-bold">内存使用率</span>
                                    <span class="text-sm" id="memory-percentage">0%</span>
                                </div>
                                <div class="progress mb-3">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 0%" id="memory-usage"></div>
                                </div>
                            </div>
                        
                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="text-sm font-weight-bold">磁盘使用率</span>
                                    <span class="text-sm" id="disk-percentage">0%</span>
                                </div>
                                <div class="progress mb-3">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 0%" id="disk-usage"></div>
                                </div>
                            </div>

                            <hr>
                        
                            <h6 class="font-weight-bold mb-3">服务状态</h6>
                            <div id="service-status">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="text-sm">管理平台</span>
                                    <span class="badge bg-success">运行中</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="text-sm">DecoyWatch</span>
                                    <span class="badge bg-secondary" id="decoywatch-status">检查中...</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="text-sm">WebSocket</span>
                                    <span class="badge bg-info" id="websocket-status">连接中...</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-sm">数据库</span>
                                    <span class="badge bg-success" id="database-status">正常</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 威胁情报概览 -->
            <div class="row mb-4">
                <div class="col-lg-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-shield-exclamation me-2"></i>威胁情报概览
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="h4 font-weight-bold text-danger" id="high-risk-ips">0</div>
                                        <div class="text-xs text-uppercase">高危IP</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="h4 font-weight-bold text-warning" id="suspicious-activities">0</div>
                                        <div class="text-xs text-uppercase">可疑活动</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="h4 font-weight-bold text-info" id="blocked-attempts">0</div>
                                        <div class="text-xs text-uppercase">拦截次数</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="h4 font-weight-bold text-success" id="honeypot-interactions">0</div>
                                        <div class="text-xs text-uppercase">蜜罐交互</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-lightning me-2"></i>快速操作
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <a href="/nodes" class="btn btn-outline-primary w-100">
                                        <i class="bi bi-hdd-network d-block mb-2" style="font-size: 1.5rem;"></i>
                                        <span class="d-block">节点管理</span>
                                    </a>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <a href="/templates" class="btn btn-outline-success w-100">
                                        <i class="bi bi-layers d-block mb-2" style="font-size: 1.5rem;"></i>
                                        <span class="d-block">模板管理</span>
                                    </a>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <a href="/deployments" class="btn btn-outline-warning w-100">
                                        <i class="bi bi-play-circle d-block mb-2" style="font-size: 1.5rem;"></i>
                                        <span class="d-block">部署管理</span>
                                    </a>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <a href="/intelligence" class="btn btn-outline-danger w-100">
                                        <i class="bi bi-shield-exclamation d-block mb-2" style="font-size: 1.5rem;"></i>
                                        <span class="d-block">情报数据</span>
                                    </a>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <button class="btn btn-outline-info w-100" onclick="showSystemLogs()">
                                        <i class="bi bi-file-text d-block mb-2" style="font-size: 1.5rem;"></i>
                                        <span class="d-block">系统日志</span>
                                    </button>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <button class="btn btn-outline-secondary w-100" onclick="showSettings()">
                                        <i class="bi bi-gear d-block mb-2" style="font-size: 1.5rem;"></i>
                                        <span class="d-block">系统设置</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/static/libs/bootstrap/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="/static/libs/chart.js/chart.min.js"></script>
    <!-- 自定义JS -->
    <script src="/static/js/dashboard.js"></script>
</body>
</html>
