/* CSS变量定义 - 暗蓝色主题 */
:root {
  /* 主色调 - 暗蓝色系 */
  --primary-50: #0f172a;
  --primary-100: #1e293b;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-900: #1e3a8a;

  /* 功能色彩 */
  --success-50: #0f1419;
  --success-500: #10b981;
  --success-600: #059669;
  
  --warning-50: #1c1917;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  
  --danger-50: #1f1315;
  --danger-500: #ef4444;
  --danger-600: #dc2626;
  
  --info-50: #0c1420;
  --info-500: #06b6d4;
  --info-600: #0891b2;

  /* 暗蓝色中性色 */
  --gray-50: #0f172a;
  --gray-100: #1e293b;
  --gray-200: #334155;
  --gray-300: #475569;
  --gray-400: #64748b;
  --gray-500: #94a3b8;
  --gray-600: #cbd5e1;
  --gray-700: #e2e8f0;
  --gray-800: #f1f5f9;
  --gray-900: #f8fafc;

  /* 暗蓝色背景 */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;

  /* 阴影系统 - 暗色调整 */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.5), 0 2px 4px -2px rgb(0 0 0 / 0.5);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.6), 0 4px 6px -4px rgb(0 0 0 / 0.6);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.7), 0 8px 10px -6px rgb(0 0 0 / 0.7);

  /* 圆角系统 */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* 间距系统 */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;

  /* 字体系统 */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

  /* 过渡动画 */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* 全局重置和基础样式 - 暗蓝色主题 */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-sans);
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--gray-100) 100%);
  color: var(--gray-800);
  line-height: 1.6;
  margin: 0;
  padding: 0;
  font-size: 14px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 导航栏暗蓝色设计 */
.navbar {
  backdrop-filter: blur(20px);
  background: rgba(15, 23, 42, 0.95) !important;
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-lg);
}

.bg-primary {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%) !important;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.375rem;
  color: var(--gray-800) !important;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.nav-link {
  font-weight: 500;
  color: var(--gray-700) !important;
  padding: var(--space-2) var(--space-4) !important;
  border-radius: var(--radius-md);
  margin: 0 var(--space-1);
  transition: var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
  transition: left 0.5s;
}

.nav-link:hover::before {
  left: 100%;
}

.nav-link:hover {
  background: rgba(59, 130, 246, 0.15);
  color: var(--gray-900) !important;
  transform: translateY(-1px);
}

.nav-link.active {
  background: rgba(59, 130, 246, 0.2);
  color: var(--gray-900) !important;
  box-shadow: inset 0 1px 0 rgba(59, 130, 246, 0.3);
}

/* 现代化卡片系统 - 暗蓝色主题 */
.card {
  background: var(--bg-secondary);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  overflow: hidden;
  position: relative;
  backdrop-filter: blur(10px);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-500), var(--info-500), var(--success-500));
  opacity: 0;
  transition: var(--transition-normal);
}

.card:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-500);
  background: var(--bg-tertiary);
}

.card:hover::before {
  opacity: 1;
}

.card-header {
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-5) var(--space-6);
  font-weight: 700;
  color: var(--gray-800);
  position: relative;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: var(--space-6);
  right: var(--space-6);
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-500), transparent);
}

.card-body {
  padding: var(--space-6);
  position: relative;
  color: var(--gray-700);
}

.card-footer {
  background: var(--gray-100);
  border-top: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-6);
  color: var(--gray-600);
  font-size: 0.875rem;
}

/* 边框装饰系统 - 暗蓝色主题 */
.border-left-primary {
  border-left: 4px solid var(--primary-500) !important;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, transparent 100%);
}

.border-left-success {
  border-left: 4px solid var(--success-500) !important;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, transparent 100%);
}

.border-left-warning {
  border-left: 4px solid var(--warning-500) !important;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, transparent 100%);
}

.border-left-info {
  border-left: 4px solid var(--info-500) !important;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.1) 0%, transparent 100%);
}

/* 现代化按钮系统 */
.btn {
  font-weight: 500;
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-md);
  border: none;
  transition: var(--transition-fast);
  position: relative;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  text-decoration: none;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: white;
}

.btn-outline-primary {
  background: transparent;
  border: 2px solid var(--primary-500);
  color: var(--primary-600);
}

.btn-outline-primary:hover {
  background: var(--primary-500);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-outline-secondary {
  background: transparent;
  border: 2px solid var(--gray-300);
  color: var(--gray-600);
}

.btn-outline-secondary:hover {
  background: var(--gray-600);
  color: white;
  border-color: var(--gray-600);
}

.btn-outline-success {
  background: transparent;
  border: 2px solid var(--success-500);
  color: var(--success-600);
}

.btn-outline-success:hover {
  background: var(--success-500);
  color: white;
}

.btn-outline-warning {
  background: transparent;
  border: 2px solid var(--warning-500);
  color: var(--warning-600);
}

.btn-outline-warning:hover {
  background: var(--warning-500);
  color: white;
}

.btn-outline-danger {
  background: transparent;
  border: 2px solid var(--danger-500);
  color: var(--danger-600);
}

.btn-outline-danger:hover {
  background: var(--danger-500);
  color: white;
}

.btn-outline-info {
  background: transparent;
  border: 2px solid var(--info-500);
  color: var(--info-600);
}

.btn-outline-info:hover {
  background: var(--info-500);
  color: white;
}

.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: 0.875rem;
}

/* 现代化表格设计 - 暗蓝色主题 */
.table {
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  margin-bottom: 0;
}

.table thead {
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  position: relative;
}

.table thead::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-500), var(--info-500), var(--success-500));
}

.table thead th {
  border: none;
  font-weight: 700;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.1em;
  color: var(--gray-800);
  padding: var(--space-5) var(--space-6);
  position: relative;
}

.table thead th:first-child {
  padding-left: var(--space-8);
}

.table thead th:last-child {
  padding-right: var(--space-8);
}

.table tbody td {
  border-color: var(--gray-200);
  padding: var(--space-5) var(--space-6);
  vertical-align: middle;
  color: var(--gray-700);
  font-weight: 500;
  position: relative;
  background: var(--bg-secondary);
}

.table tbody td:first-child {
  padding-left: var(--space-8);
  font-weight: 600;
  color: var(--gray-800);
}

.table tbody td:last-child {
  padding-right: var(--space-8);
}

.table tbody tr {
  transition: var(--transition-fast);
  position: relative;
}

.table tbody tr::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--primary-500);
  transform: scaleY(0);
  transition: var(--transition-fast);
}

.table tbody tr:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, var(--bg-tertiary) 100%);
  transform: translateX(4px);
}

.table tbody tr:hover::before {
  transform: scaleY(1);
}

.table tbody tr:hover td {
  background: transparent;
  color: var(--gray-800);
}

.table tbody tr:nth-child(even) td {
  background: var(--bg-primary);
}

.table tbody tr:nth-child(even):hover td {
  background: transparent;
}

.table-responsive {
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
}

/* 表格状态样式 */
.table .status-online {
  color: var(--success-600);
  font-weight: 600;
}

.table .status-offline {
  color: var(--danger-600);
  font-weight: 600;
}

.table .status-pending {
  color: var(--warning-600);
  font-weight: 600;
}

/* 表格操作按钮 */
.table .btn-group {
  display: flex;
  gap: var(--space-2);
}

.table .btn-sm {
  padding: var(--space-1) var(--space-3);
  font-size: 0.75rem;
  border-radius: var(--radius-sm);
}

/* 卡片统计样式 */
.card.stat-card {
  border-left: 4px solid var(--primary-500);
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--bg-primary) 100%);
}

.card.stat-card.success {
  border-left-color: var(--success-500);
  background: linear-gradient(135deg, var(--success-50) 0%, var(--bg-primary) 100%);
}

.card.stat-card.warning {
  border-left-color: var(--warning-500);
  background: linear-gradient(135deg, var(--warning-50) 0%, var(--bg-primary) 100%);
}

.card.stat-card.danger {
  border-left-color: var(--danger-500);
  background: linear-gradient(135deg, var(--danger-50) 0%, var(--bg-primary) 100%);
}

.card.stat-card.info {
  border-left-color: var(--info-500);
  background: linear-gradient(135deg, var(--info-50) 0%, var(--bg-primary) 100%);
}

/* 卡片图标样式 */
.card-icon {
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  box-shadow: var(--shadow-md);
}

.card-icon.success {
  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
}

.card-icon.warning {
  background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
}

.card-icon.danger {
  background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%);
}

.card-icon.info {
  background: linear-gradient(135deg, var(--info-500) 0%, var(--info-600) 100%);
}

/* 表格排序样式 */
.table thead th.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
}

.table thead th.sortable:hover {
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
}

.table thead th.sortable::after {
  content: '↕';
  position: absolute;
  right: var(--space-3);
  opacity: 0.5;
  transition: var(--transition-fast);
}

.table thead th.sortable.asc::after {
  content: '↑';
  opacity: 1;
  color: var(--primary-600);
}

.table thead th.sortable.desc::after {
  content: '↓';
  opacity: 1;
  color: var(--primary-600);
}

/* 现代化徽章系统 */
.badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
}

.badge.bg-success {
  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%) !important;
  color: white;
  box-shadow: var(--shadow-sm);
}

.badge.bg-warning {
  background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%) !important;
  color: white;
  box-shadow: var(--shadow-sm);
}

.badge.bg-danger {
  background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%) !important;
  color: white;
  box-shadow: var(--shadow-sm);
}

.badge.bg-info {
  background: linear-gradient(135deg, var(--info-500) 0%, var(--info-600) 100%) !important;
  color: white;
  box-shadow: var(--shadow-sm);
}

.badge.bg-secondary {
  background: linear-gradient(135deg, var(--gray-500) 0%, var(--gray-600) 100%) !important;
  color: white;
  box-shadow: var(--shadow-sm);
}

/* 现代化进度条 */
.progress {
  height: 0.75rem;
  background: var(--gray-200);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
  border-radius: var(--radius-md);
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.progress-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.progress-bar.bg-primary {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%) !important;
}

.progress-bar.bg-success {
  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%) !important;
}

.progress-bar.bg-warning {
  background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%) !important;
}

/* 下拉菜单现代化 */
.dropdown-menu {
  background: var(--bg-primary);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--space-2);
  backdrop-filter: blur(20px);
}

.dropdown-item {
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
  color: var(--gray-700);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.dropdown-item:hover {
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
  color: var(--primary-700);
}

/* 加载动画现代化 */
.spinner-border {
  width: 2rem;
  height: 2rem;
  border: 0.25em solid var(--gray-300);
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-rotate 0.75s linear infinite;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.125em;
}

@keyframes spinner-rotate {
  to { transform: rotate(360deg); }
}

/* 文本颜色系统 - 暗蓝色主题 */
.text-primary { color: var(--primary-500) !important; }
.text-success { color: var(--success-500) !important; }
.text-warning { color: var(--warning-500) !important; }
.text-danger { color: var(--danger-500) !important; }
.text-info { color: var(--info-500) !important; }
.text-muted { color: var(--gray-500) !important; }
.text-gray-300 { color: var(--gray-300) !important; }
.text-gray-400 { color: var(--gray-400) !important; }
.text-gray-500 { color: var(--gray-500) !important; }
.text-gray-800 { color: var(--gray-800) !important; }

/* 字体大小系统 */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }

.font-weight-bold { font-weight: 700 !important; }
.text-uppercase { text-transform: uppercase; }

/* 标题系统 */
.h4 { font-size: 1.5rem; font-weight: 600; line-height: 1.2; color: var(--gray-800); }
.h5 { font-size: 1.25rem; font-weight: 600; line-height: 1.2; color: var(--gray-800); }
.h6 { font-size: 1rem; font-weight: 600; line-height: 1.2; color: var(--gray-800); }

/* 间距工具类 */
.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--space-1) !important; }
.mb-2 { margin-bottom: var(--space-2) !important; }
.mb-3 { margin-bottom: var(--space-3) !important; }
.mb-4 { margin-bottom: var(--space-4) !important; }
.mt-3 { margin-top: var(--space-3) !important; }
.mt-4 { margin-top: var(--space-4) !important; }
.me-1 { margin-right: var(--space-1) !important; }
.me-2 { margin-right: var(--space-2) !important; }
.mr-2 { margin-right: var(--space-2) !important; }

.py-2 { padding-top: var(--space-2) !important; padding-bottom: var(--space-2) !important; }
.py-3 { padding-top: var(--space-3) !important; padding-bottom: var(--space-3) !important; }

/* 布局工具类 */
.d-flex { display: flex !important; }
.d-block { display: block !important; }
.justify-content-between { justify-content: space-between !important; }
.align-items-center { align-items: center !important; }
.text-center { text-align: center !important; }
.text-end { text-align: right !important; }
.h-100 { height: 100% !important; }
.w-100 { width: 100% !important; }

/* 阴影工具类 */
.shadow { box-shadow: var(--shadow-md) !important; }

/* 无边距行 */
.no-gutters {
  margin-right: 0;
  margin-left: 0;
}

.no-gutters > .col,
.no-gutters > [class*="col-"] {
  padding-right: 0;
  padding-left: 0;
}

/* 图标样式 */
.bi {
  vertical-align: -0.125em;
}

/* 自定义滚动条 - 暗蓝色主题 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-500);
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card {
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-body {
    padding: var(--space-4);
  }
  
  .btn {
    padding: var(--space-2) var(--space-4);
    font-size: 0.875rem;
  }
  
  .btn.w-100 {
    margin-bottom: var(--space-2);
  }
  
  .h5 {
    font-size: 1.1rem;
  }
  
  .text-xs {
    font-size: 0.65rem;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1f2937;
    --bg-secondary: #111827;
    --bg-tertiary: #0f172a;
    --gray-50: #374151;
    --gray-100: #4b5563;
    --gray-200: #6b7280;
    --gray-800: #f9fafb;
    --gray-700: #e5e7eb;
  }
}

/* 特殊效果 */
.glass-effect {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-700) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 微交互动画 */
.hover-lift {
  transition: var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* 顶部统计卡片样式 */
.stat-card-mini {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  border-left: 4px solid var(--primary-500);
  height: 100%;
}

.stat-card-mini:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-card-mini.border-left-primary {
  border-left-color: var(--primary-500);
}

.stat-card-mini.border-left-success {
  border-left-color: var(--success-500);
}

.stat-card-mini.border-left-info {
  border-left-color: var(--info-500);
}

.stat-card-mini.border-left-warning {
  border-left-color: var(--warning-500);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.stat-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  flex-shrink: 0;
}

.stat-card-mini.border-left-success .stat-icon {
  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
}

.stat-card-mini.border-left-info .stat-icon {
  background: linear-gradient(135deg, var(--info-500) 0%, var(--info-600) 100%);
}

.stat-card-mini.border-left-warning .stat-icon {
  background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
}

.stat-info {
  flex: 1;
  min-width: 0;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gray-800);
  line-height: 1.2;
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* 侧边栏快速操作样式 */
.sidebar-actions {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--space-5);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--space-4);
}

.sidebar-title {
  font-size: 0.875rem;
  font-weight: 700;
  color: var(--gray-800);
  margin-bottom: var(--space-4);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.btn-sidebar {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  border: none;
  background: transparent;
  color: var(--gray-700);
  font-weight: 500;
  font-size: 0.875rem;
  transition: var(--transition-fast);
  text-align: left;
  width: 100%;
}

.btn-sidebar:hover {
  background: var(--primary-50);
  color: var(--primary-600);
  transform: translateX(2px);
}

.btn-sidebar.outline {
  border: 1px solid var(--gray-200);
}

.btn-sidebar.outline:hover {
  border-color: var(--primary-200);
  background: var(--primary-50);
}

.btn-sidebar i {
  font-size: 1rem;
  width: 16px;
  text-align: center;
}

/* 内容布局调整 */
.content-wrapper {
  padding: var(--space-6);
}

.content-layout {
  display: flex;
  gap: var(--space-6);
  align-items: flex-start;
}

.content-sidebar {
  width: 280px;
  flex-shrink: 0;
}

.content-main {
  flex: 1;
  min-width: 0;
}

/* 搜索过滤区域样式 */
.search-filter-section {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--space-5);
  margin-bottom: var(--space-4);
  box-shadow: var(--shadow-sm);
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .content-layout {
    flex-direction: column;
  }
  
  .content-sidebar {
    width: 100%;
    order: 2;
  }
  
  .content-main {
    order: 1;
  }
  
  .action-buttons {
    flex-direction: row;
    flex-wrap: wrap;
  }
  
  .btn-sidebar {
    flex: 1;
    min-width: 120px;
  }
}

@media (max-width: 768px) {
  .content-wrapper {
    padding: var(--space-4);
  }
  
  .stat-content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-2);
  }
  
  .stat-icon {
    width: 2rem;
    height: 2rem;
    font-size: 1rem;
  }
  
  .stat-value {
    font-size: 1.25rem;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .btn-sidebar {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .stat-card-mini {
    margin-bottom: var(--space-3);
  }
  
  .content-layout {
    gap: var(--space-4);
  }
  
  .sidebar-actions {
    padding: var(--space-4);
  }
}

/* 加载状态优化 */
.stat-value .spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.1em;
}

/* 统计卡片动画 */
.stat-card-mini {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card-mini:nth-child(1) { animation-delay: 0.1s; }
.stat-card-mini:nth-child(2) { animation-delay: 0.2s; }
.stat-card-mini:nth-child(3) { animation-delay: 0.3s; }
.stat-card-mini:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
