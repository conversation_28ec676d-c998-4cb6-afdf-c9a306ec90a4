/**
 * 现代化字体系统
 * 支持中英文混排，优化阅读体验
 */

/* Inter 字体定义 */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url('/static/assets/fonts/Inter-Thin.woff2') format('woff2'),
       url('/static/assets/fonts/Inter-Thin.woff') format('woff');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url('/static/assets/fonts/Inter-ExtraLight.woff2') format('woff2'),
       url('/static/assets/fonts/Inter-ExtraLight.woff') format('woff');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url('/static/assets/fonts/Inter-Light.woff2') format('woff2'),
       url('/static/assets/fonts/Inter-Light.woff') format('woff');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('/static/assets/fonts/Inter-Regular.woff2') format('woff2'),
       url('/static/assets/fonts/Inter-Regular.woff') format('woff');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url('/static/assets/fonts/Inter-Medium.woff2') format('woff2'),
       url('/static/assets/fonts/Inter-Medium.woff') format('woff');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('/static/assets/fonts/Inter-SemiBold.woff2') format('woff2'),
       url('/static/assets/fonts/Inter-SemiBold.woff') format('woff');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('/static/assets/fonts/Inter-Bold.woff2') format('woff2'),
       url('/static/assets/fonts/Inter-Bold.woff') format('woff');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url('/static/assets/fonts/Inter-ExtraBold.woff2') format('woff2'),
       url('/static/assets/fonts/Inter-ExtraBold.woff') format('woff');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url('/static/assets/fonts/Inter-Black.woff2') format('woff2'),
       url('/static/assets/fonts/Inter-Black.woff') format('woff');
}

/* 可变字体版本 (推荐) */
@font-face {
  font-family: 'Inter Variable';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url('/static/assets/fonts/Inter.var.woff2') format('woff2');
}

/* 字体栈定义 */
:root {
  /* 主要字体栈 - 优先使用Inter，回退到系统字体 */
  --font-family-sans: 'Inter Variable', 'Inter', -apple-system, BlinkMacSystemFont, 
                       'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 
                       'Helvetica Neue', Helvetica, Arial, sans-serif;
  
  /* 等宽字体栈 */
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', 
                       Consolas, 'Courier New', monospace;
  
  /* 中文优化字体栈 */
  --font-family-chinese: 'Inter Variable', 'Inter', 'PingFang SC', 'Hiragino Sans GB', 
                          'Source Han Sans SC', 'Noto Sans CJK SC', 'Microsoft YaHei', 
                          'WenQuanYi Micro Hei', sans-serif;
}

/* 字体大小系统 */
:root {
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 3.75rem;   /* 60px */
}

/* 行高系统 */
:root {
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
}

/* 字重系统 */
:root {
  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
}

/* 基础字体应用 */
html {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  font-weight: var(--font-weight-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  font-weight: inherit;
}

/* 中文内容优化 */
.text-chinese {
  font-family: var(--font-family-chinese);
  font-feature-settings: 'kern' 1;
}

/* 等宽字体 */
.font-mono {
  font-family: var(--font-family-mono);
}

/* 字体大小工具类 */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }
.text-5xl { font-size: var(--font-size-5xl); }
.text-6xl { font-size: var(--font-size-6xl); }

/* 字重工具类 */
.font-thin { font-weight: var(--font-weight-thin); }
.font-extralight { font-weight: var(--font-weight-extralight); }
.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }
.font-extrabold { font-weight: var(--font-weight-extrabold); }
.font-black { font-weight: var(--font-weight-black); }

/* 行高工具类 */
.leading-none { line-height: var(--line-height-none); }
.leading-tight { line-height: var(--line-height-tight); }
.leading-snug { line-height: var(--line-height-snug); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }
.leading-loose { line-height: var(--line-height-loose); }

/* 字体特性优化 */
.font-feature-default {
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
}

.font-feature-numeric {
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1, 'tnum' 1;
}

/* 响应式字体大小 */
@media (max-width: 640px) {
  html {
    font-size: 14px;
  }
}

@media (min-width: 1024px) {
  html {
    font-size: 16px;
  }
}
