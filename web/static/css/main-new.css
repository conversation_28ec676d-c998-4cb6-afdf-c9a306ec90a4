/**
 * 主CSS文件
 * 导入所有样式模块
 */

/* ========================================
   基础样式 (Base Styles)
   ======================================== */

/* 设计令牌 - 必须最先导入 */
@import url('./base/tokens.css');

/* CSS重置 */
@import url('./base/reset.css');

/* 字体系统 */
@import url('./base/fonts.css');

/* ========================================
   全局样式 (Global Styles)
   ======================================== */

html {
  scroll-behavior: smooth;
}

body {
  min-height: 100vh;
  background-color: var(--color-background);
  color: var(--color-text-primary);
  transition: var(--transition-base);
}

/* 全局容器 */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 页面容器 */
.page {
  flex: 1;
  padding: var(--space-6);
}

/* 页面标题 */
.page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-6);
}

/* 页面副标题 */
.page-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-4);
}

/* 分割线 */
.divider {
  height: 1px;
  background-color: var(--color-border);
  margin: var(--space-6) 0;
}

/* 响应式图片 */
.img-responsive {
  max-width: 100%;
  height: auto;
  display: block;
}

/* 圆形图片 */
.img-circle {
  border-radius: var(--radius-full);
}

/* 图标基础样式 */
.icon {
  display: inline-block;
  width: 1em;
  height: 1em;
  fill: currentColor;
  vertical-align: middle;
}

/* 图标大小变体 */
.icon-xs { width: 0.75rem; height: 0.75rem; }
.icon-sm { width: 1rem; height: 1rem; }
.icon-md { width: 1.25rem; height: 1.25rem; }
.icon-lg { width: 1.5rem; height: 1.5rem; }
.icon-xl { width: 2rem; height: 2rem; }

/* 动画类 */
.animate-fade-in {
  animation: fadeIn var(--duration-300) var(--ease-out);
}

.animate-slide-up {
  animation: slideUp var(--duration-300) var(--ease-out);
}

.animate-bounce {
  animation: bounce var(--duration-500) var(--ease-bounce);
}

/* 动画定义 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

/* 临时兼容性样式 - 保持现有功能正常 */
.navbar {
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-800) 100%) !important;
  box-shadow: var(--shadow-md);
  border: none;
  padding: var(--space-3) var(--space-4);
}

.sidebar {
  position: fixed;
  top: 76px;
  left: 0;
  width: 260px;
  height: calc(100vh - 76px);
  background: linear-gradient(180deg, var(--color-primary-700) 0%, var(--color-primary-900) 100%);
  overflow-y: auto;
  z-index: var(--z-fixed);
  box-shadow: var(--shadow-md);
}

.main-content {
  margin-left: 260px;
  min-height: calc(100vh - 76px);
  background: var(--color-surface);
  padding: var(--space-8);
}

.card {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-base);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-2) var(--space-4);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: var(--transition-fast);
  cursor: pointer;
}

.btn-primary {
  background-color: var(--color-primary-600);
  color: white;
}

.btn-primary:hover {
  background-color: var(--color-primary-700);
}

.alert {
  padding: var(--space-4) var(--space-5);
  border-radius: var(--radius-md);
  border-left: 4px solid;
  margin-bottom: var(--space-4);
}

.alert-success {
  background-color: var(--color-success-50);
  color: var(--color-success-800);
  border-left-color: var(--color-success-500);
}

.alert-danger {
  background-color: var(--color-danger-50);
  color: var(--color-danger-800);
  border-left-color: var(--color-danger-500);
}

.alert-info {
  background-color: var(--color-info-50);
  color: var(--color-info-800);
  border-left-color: var(--color-info-500);
}

.alert-warning {
  background-color: var(--color-warning-50);
  color: var(--color-warning-800);
  border-left-color: var(--color-warning-500);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform var(--duration-300) var(--ease-out);
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
    padding: var(--space-4);
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .page {
    padding: 0;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
}
