/**
 * 图标管理模块
 * 提供SVG图标的加载和使用功能，替代Font Awesome CDN
 */

class IconManager {
    constructor() {
        this.iconCache = new Map();
        this.iconPath = '/static/assets/icons/';
        
        // Font Awesome 5/6 图标名称映射
        this.iconMapping = {
            'fa-shield-alt': 'shield-halved',
            'fa-tachometer-alt': 'gauge-high',
            'fa-server': 'server',
            'fa-cube': 'cube',
            'fa-exclamation-triangle': 'triangle-exclamation',
            'fa-user': 'user',
            'fa-user-cog': 'user-gear',
            'fa-sign-out-alt': 'right-from-bracket',
            'fa-sign-in-alt': 'right-to-bracket',
            'fa-home': 'house',
            'fa-eye': 'eye',
            'fa-heartbeat': 'heart-pulse',
            'fa-rocket': 'rocket',
            'fa-bug': 'bug',
            'fa-chart-line': 'chart-line',
            'fa-file-alt': 'file-lines',
            'fa-users': 'users',
            'fa-cog': 'gear',
            'fa-list': 'list',
            'fa-check-circle': 'circle-check',
            'fa-exclamation-circle': 'circle-exclamation',
            'fa-info-circle': 'circle-info'
        };
    }

    /**
     * 加载SVG图标
     * @param {string} iconName - 图标名称
     * @returns {Promise<string>} SVG内容
     */
    async loadIcon(iconName) {
        // 移除fa-前缀并映射到新名称
        const cleanName = iconName.replace(/^fa-/, '');
        const mappedName = this.iconMapping[`fa-${cleanName}`] || cleanName;
        
        if (this.iconCache.has(mappedName)) {
            return this.iconCache.get(mappedName);
        }

        try {
            const response = await fetch(`${this.iconPath}${mappedName}.svg`);
            if (!response.ok) {
                throw new Error(`图标加载失败: ${mappedName}`);
            }
            
            const svgContent = await response.text();
            this.iconCache.set(mappedName, svgContent);
            return svgContent;
        } catch (error) {
            console.warn(`无法加载图标 ${mappedName}:`, error);
            return this.getFallbackIcon();
        }
    }

    /**
     * 替换页面中的Font Awesome图标
     */
    async replaceIcons() {
        const iconElements = document.querySelectorAll('i[class*="fa-"]');
        
        for (const element of iconElements) {
            const classList = Array.from(element.classList);
            const iconClass = classList.find(cls => cls.startsWith('fa-') && cls !== 'fas' && cls !== 'far' && cls !== 'fab');
            
            if (iconClass) {
                try {
                    const svgContent = await this.loadIcon(iconClass);
                    const svgElement = this.createSVGElement(svgContent, element.className);
                    element.parentNode.replaceChild(svgElement, element);
                } catch (error) {
                    console.warn(`替换图标失败: ${iconClass}`, error);
                }
            }
        }
    }

    /**
     * 创建SVG元素
     * @param {string} svgContent - SVG内容
     * @param {string} className - CSS类名
     * @returns {Element} SVG元素
     */
    createSVGElement(svgContent, className) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(svgContent, 'image/svg+xml');
        const svg = doc.documentElement;
        
        // 设置默认属性
        svg.setAttribute('class', className.replace(/fa[srb]?\s+/, 'icon '));
        svg.setAttribute('width', '1em');
        svg.setAttribute('height', '1em');
        svg.setAttribute('fill', 'currentColor');
        
        return svg;
    }

    /**
     * 获取备用图标
     * @returns {string} 备用SVG内容
     */
    getFallbackIcon() {
        return `<svg viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
            <path d="M256 512c141.4 0 256-114.6 256-256S397.4 0 256 0S0 114.6 0 256S114.6 512 256 512z"/>
        </svg>`;
    }

    /**
     * 预加载常用图标
     */
    async preloadIcons() {
        const commonIcons = [
            'shield-halved', 'gauge-high', 'server', 'cube', 'triangle-exclamation',
            'user', 'user-gear', 'house', 'eye', 'gear', 'users'
        ];

        const loadPromises = commonIcons.map(icon => this.loadIcon(icon));
        await Promise.allSettled(loadPromises);
        console.log('常用图标预加载完成');
    }
}

// 导出单例
export const iconManager = new IconManager();

// 自动初始化
document.addEventListener('DOMContentLoaded', async () => {
    await iconManager.preloadIcons();
    await iconManager.replaceIcons();
});
