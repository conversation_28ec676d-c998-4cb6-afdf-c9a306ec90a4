// 情报数据页面JavaScript

// 全局变量
let attackTypesChart = null;
let hourlyAttacksChart = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查认证状态
    checkAuth();
    
    // 初始化页面
    initializePage();
    
    // 绑定事件
    bindEvents();
    
    // 设置导航激活状态
    setActiveNavLink();
});

// 侧边栏切换功能
function toggleSidebar() {
    const sidebar = document.getElementById('mainSidebar');
    if (sidebar) {
        sidebar.classList.toggle('show');
    }
}

// 用户菜单切换
function toggleUserMenu() {
    // 用户菜单切换逻辑
    console.log('Toggle user menu');
}

// 设置导航激活状态
function setActiveNavLink() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
}

// 检查认证状态
function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = '/login';
        return;
    }
    
    // 设置用户信息
    const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
    if (userInfo.username) {
        const usernameElement = document.getElementById('sidebar-username');
        if (usernameElement) {
            usernameElement.textContent = userInfo.username;
        }
    }
}

// 初始化页面
function initializePage() {
    // 检查DecoyWatch服务健康状态
    checkDecoyWatchHealth();

    // 加载统计数据
    loadAttackStatistics();

    // 加载IP统计数据
    loadIPStatistics();

    // 初始化图表
    initializeCharts();

    // 初始化分析页面
    initializeAnalysisPage();
}

// 绑定事件
function bindEvents() {
    // 退出登录
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
}

// 检查DecoyWatch健康状态
async function checkDecoyWatchHealth() {
    const statusElement = document.getElementById('service-status');
    
    try {
        const response = await fetch('/api/v1/intelligence/health', {
            headers: getAuthHeaders()
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                statusElement.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        <span class="text-success">DecoyWatch服务正常</span>
                        <small class="text-muted ms-2">(检查时间: ${new Date().toLocaleTimeString()})</small>
                    </div>
                `;
            } else {
                throw new Error(data.message);
            }
        } else {
            throw new Error(`HTTP ${response.status}`);
        }
    } catch (error) {
        console.error('Health check failed:', error);
        statusElement.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="bi bi-exclamation-triangle-fill text-warning me-2"></i>
                <span class="text-warning">DecoyWatch服务异常</span>
                <small class="text-muted ms-2">${error.message}</small>
            </div>
        `;
    }
}

// 加载攻击统计数据
async function loadAttackStatistics() {
    try {
        const response = await fetch('/api/v1/intelligence/statistics', {
            headers: getAuthHeaders()
        });
        
        const data = await handleApiResponse(response);
        
        // 更新统计卡片
        document.getElementById('total-attacks').textContent = formatNumber(data.total_attacks || 0);
        document.getElementById('today-attacks').textContent = formatNumber(data.today_attacks || 0);
        document.getElementById('unique-ips').textContent = formatNumber(data.unique_ips || 0);
        
        // 更新图表数据
        updateAttackTypesChart(data.top_attack_types || []);
        updateHourlyAttacksChart(data.hourly_stats || []);
        updateTopPortsTable(data.top_target_ports || []);
        
    } catch (error) {
        console.error('Load attack statistics failed:', error);
        showError('加载攻击统计数据失败: ' + error.message);
    }
}

// 加载IP统计数据
async function loadIPStatistics() {
    try {
        const response = await fetch('/api/v1/intelligence/ip-statistics?limit=10', {
            headers: getAuthHeaders()
        });
        
        const data = await handleApiResponse(response);
        
        updateTopIPsTable(data || []);
        
    } catch (error) {
        console.error('Load IP statistics failed:', error);
        showError('加载IP统计数据失败: ' + error.message);
    }
}

// 初始化图表
function initializeCharts() {
    // 攻击类型分布饼图
    const attackTypesCtx = document.getElementById('attack-types-chart').getContext('2d');
    attackTypesChart = new Chart(attackTypesCtx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // 24小时攻击趋势线图
    const hourlyAttacksCtx = document.getElementById('hourly-attacks-chart').getContext('2d');
    hourlyAttacksChart = new Chart(hourlyAttacksCtx, {
        type: 'line',
        data: {
            labels: Array.from({length: 24}, (_, i) => `${i}:00`),
            datasets: [{
                label: '攻击次数',
                data: new Array(24).fill(0),
                borderColor: '#36A2EB',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// 更新攻击类型图表
function updateAttackTypesChart(attackTypes) {
    if (!attackTypesChart) return;
    
    const labels = attackTypes.map(item => item.attack_type || item.type);
    const data = attackTypes.map(item => item.count);
    
    attackTypesChart.data.labels = labels;
    attackTypesChart.data.datasets[0].data = data;
    attackTypesChart.update();
}

// 更新24小时攻击趋势图表
function updateHourlyAttacksChart(hourlyStats) {
    if (!hourlyAttacksChart) return;
    
    const data = new Array(24).fill(0);
    hourlyStats.forEach(stat => {
        if (stat.hour >= 0 && stat.hour < 24) {
            data[stat.hour] = stat.count;
        }
    });
    
    hourlyAttacksChart.data.datasets[0].data = data;
    hourlyAttacksChart.update();
}

// 更新Top IP表格
function updateTopIPsTable(ipStats) {
    const tbody = document.getElementById('top-ips-table');
    
    if (ipStats.length === 0) {
        tbody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }
    
    tbody.innerHTML = ipStats.map(ip => `
        <tr>
            <td><code>${ip.source_ip || ip.ip}</code></td>
            <td><span class="badge bg-danger">${formatNumber(ip.count)}</span></td>
            <td>${ip.country || '-'}</td>
        </tr>
    `).join('');
}

// 更新Top端口表格
function updateTopPortsTable(portStats) {
    const tbody = document.getElementById('top-ports-table');
    
    if (portStats.length === 0) {
        tbody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }
    
    tbody.innerHTML = portStats.map(port => `
        <tr>
            <td><code>${port.target_port || port.port}</code></td>
            <td><span class="badge bg-warning">${formatNumber(port.count)}</span></td>
            <td>${getPortService(port.target_port || port.port)}</td>
        </tr>
    `).join('');
}

// 获取端口对应的服务名
function getPortService(port) {
    const services = {
        22: 'SSH',
        23: 'Telnet',
        25: 'SMTP',
        53: 'DNS',
        80: 'HTTP',
        110: 'POP3',
        143: 'IMAP',
        443: 'HTTPS',
        993: 'IMAPS',
        995: 'POP3S',
        3389: 'RDP',
        5432: 'PostgreSQL',
        3306: 'MySQL'
    };
    return services[port] || 'Unknown';
}

// 刷新数据
function refreshData() {
    const refreshBtn = document.querySelector('button[onclick="refreshData()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>刷新中...';
    refreshBtn.disabled = true;
    
    Promise.all([
        checkDecoyWatchHealth(),
        loadAttackStatistics(),
        loadIPStatistics()
    ]).finally(() => {
        setTimeout(() => {
            refreshBtn.innerHTML = originalText;
            refreshBtn.disabled = false;
        }, 1000);
    });
}

// 格式化数字
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token');
        localStorage.removeItem('user_info');
        window.location.href = '/login';
    }
}

// 工具函数：获取认证头
function getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };
}

// 工具函数：处理API响应
async function handleApiResponse(response) {
    if (!response.ok) {
        if (response.status === 401) {
            localStorage.removeItem('token');
            localStorage.removeItem('user_info');
            window.location.href = '/login';
            return;
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    if (!data.success) {
        throw new Error(data.message || '请求失败');
    }
    
    return data.data;
}

// 显示消息函数
function showError(message) {
    showAlert(message, 'danger');
}

function showSuccess(message) {
    showAlert(message, 'success');
}

function showInfo(message) {
    showAlert(message, 'info');
}

function showAlert(message, type) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alert);
    
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, type === 'success' ? 3000 : 5000);
}

// ==================== 攻击数据分析功能 ====================

// 加载攻击趋势分析
async function loadAttackTrendAnalysis(timeRange = '24h', nodeId = '') {
    try {
        const params = new URLSearchParams();
        params.append('time_range', timeRange);
        if (nodeId) {
            params.append('node_id', nodeId);
        }

        const response = await fetch(`/api/v1/intelligence/analysis/trends?${params}`, {
            headers: getAuthHeaders()
        });

        const data = await handleApiResponse(response);

        // 更新趋势分析显示
        updateTrendAnalysisDisplay(data);

        return data;
    } catch (error) {
        console.error('加载攻击趋势分析失败:', error);
        showAlert('加载攻击趋势分析失败: ' + error.message, 'error');
        return null;
    }
}

// 加载IP地理分布分析
async function loadIPGeographicAnalysis(timeRange = '24h', nodeId = '') {
    try {
        const params = new URLSearchParams();
        params.append('time_range', timeRange);
        if (nodeId) {
            params.append('node_id', nodeId);
        }

        const response = await fetch(`/api/v1/intelligence/analysis/geographic?${params}`, {
            headers: getAuthHeaders()
        });

        const data = await handleApiResponse(response);

        // 更新地理分析显示
        updateGeographicAnalysisDisplay(data);

        return data;
    } catch (error) {
        console.error('加载IP地理分布分析失败:', error);
        showAlert('加载IP地理分布分析失败: ' + error.message, 'error');
        return null;
    }
}

// 加载攻击类型分析
async function loadAttackTypeAnalysis(timeRange = '24h', nodeId = '') {
    try {
        const params = new URLSearchParams();
        params.append('time_range', timeRange);
        if (nodeId) {
            params.append('node_id', nodeId);
        }

        const response = await fetch(`/api/v1/intelligence/analysis/attack-types?${params}`, {
            headers: getAuthHeaders()
        });

        const data = await handleApiResponse(response);

        // 更新攻击类型分析显示
        updateAttackTypeAnalysisDisplay(data);

        return data;
    } catch (error) {
        console.error('加载攻击类型分析失败:', error);
        showAlert('加载攻击类型分析失败: ' + error.message, 'error');
        return null;
    }
}

// 更新趋势分析显示
function updateTrendAnalysisDisplay(data) {
    // 更新趋势摘要
    const summaryElement = document.getElementById('trend-summary');
    if (summaryElement && data.summary) {
        summaryElement.innerHTML = `
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-card">
                        <h6>平均每小时攻击</h6>
                        <h4>${formatNumber(data.summary.avg_attacks_per_hour)}</h4>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <h6>最高峰值</h6>
                        <h4>${formatNumber(data.summary.max_attacks_per_hour)}</h4>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <h6>增长率</h6>
                        <h4 class="${data.growth_rate >= 0 ? 'text-danger' : 'text-success'}">
                            ${data.growth_rate >= 0 ? '+' : ''}${data.growth_rate.toFixed(1)}%
                        </h4>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <h6>趋势方向</h6>
                        <h4 class="${getTrendDirectionClass(data.summary.trend_direction)}">
                            ${getTrendDirectionText(data.summary.trend_direction)}
                        </h4>
                    </div>
                </div>
            </div>
        `;
    }

    // 更新趋势图表
    if (data.trend_data && data.trend_data.length > 0) {
        updateTrendChart(data.trend_data);
    }

    // 更新高峰时段
    const peakHoursElement = document.getElementById('peak-hours');
    if (peakHoursElement && data.peak_hours) {
        peakHoursElement.innerHTML = data.peak_hours.map(hour =>
            `<span class="badge bg-warning me-1">${hour}:00</span>`
        ).join('');
    }
}

// 更新地理分析显示
function updateGeographicAnalysisDisplay(data) {
    // 更新国家统计
    const countryStatsElement = document.getElementById('country-stats');
    if (countryStatsElement && data.country_stats) {
        const countryHtml = data.country_stats.slice(0, 10).map(country => `
            <tr>
                <td>
                    <span class="badge bg-secondary me-2">${country.country_code}</span>
                    ${country.country}
                </td>
                <td>${formatNumber(country.attack_count)}</td>
                <td>${formatNumber(country.unique_ips)}</td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" role="progressbar"
                             style="width: ${country.percentage}%">
                            ${country.percentage.toFixed(1)}%
                        </div>
                    </div>
                </td>
            </tr>
        `).join('');

        countryStatsElement.innerHTML = countryHtml;
    }

    // 更新顶级攻击者IP
    const topAttackersElement = document.getElementById('top-attackers');
    if (topAttackersElement && data.top_attacker_ips) {
        const attackersHtml = data.top_attacker_ips.slice(0, 10).map(attacker => `
            <tr>
                <td>
                    <code>${attacker.ip}</code>
                    <span class="badge bg-${getThreatLevelClass(attacker.threat_level)} ms-2">
                        ${attacker.threat_level.toUpperCase()}
                    </span>
                </td>
                <td>${formatNumber(attacker.attack_count)}</td>
                <td>${attacker.country || 'Unknown'}</td>
                <td>
                    ${attacker.attack_types.map(type =>
                        `<span class="badge bg-secondary me-1">${type}</span>`
                    ).join('')}
                </td>
                <td>${formatDateTime(attacker.last_seen)}</td>
            </tr>
        `).join('');

        topAttackersElement.innerHTML = attackersHtml;
    }
}

// 更新攻击类型分析显示
function updateAttackTypeAnalysisDisplay(data) {
    // 更新威胁评估
    const threatAssessmentElement = document.getElementById('threat-assessment');
    if (threatAssessmentElement && data.threat_assessment) {
        const assessment = data.threat_assessment;
        threatAssessmentElement.innerHTML = `
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-card">
                        <h6>整体威胁级别</h6>
                        <h4 class="${getThreatLevelClass(assessment.overall_threat_level)}">
                            ${assessment.overall_threat_level.toUpperCase()}
                        </h4>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <h6>高威胁类型</h6>
                        <h4>${assessment.high_threat_types ? assessment.high_threat_types.length : 0}</h4>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <h6>新兴威胁</h6>
                        <h4>${assessment.emerging_threats ? assessment.emerging_threats.length : 0}</h4>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <h6>攻击类型总数</h6>
                        <h4>${data.total_attack_types}</h4>
                    </div>
                </div>
            </div>
        `;
    }

    // 更新攻击类型分布
    const typeDistributionElement = document.getElementById('type-distribution');
    if (typeDistributionElement && data.type_distribution) {
        const distributionHtml = data.type_distribution.map(type => `
            <tr>
                <td>
                    <span class="badge bg-${getSeverityClass(type.severity)} me-2">
                        ${type.severity.toUpperCase()}
                    </span>
                    ${type.attack_type}
                </td>
                <td>${formatNumber(type.count)}</td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-${getSeverityClass(type.severity)}"
                             role="progressbar" style="width: ${type.percentage}%">
                            ${type.percentage.toFixed(1)}%
                        </div>
                    </div>
                </td>
                <td>
                    <small class="text-muted">${type.description}</small>
                </td>
            </tr>
        `).join('');

        typeDistributionElement.innerHTML = distributionHtml;
    }
}

// 辅助函数
function getTrendDirectionClass(direction) {
    switch (direction) {
        case 'increasing': return 'text-danger';
        case 'decreasing': return 'text-success';
        default: return 'text-warning';
    }
}

function getTrendDirectionText(direction) {
    switch (direction) {
        case 'increasing': return '上升';
        case 'decreasing': return '下降';
        default: return '稳定';
    }
}

function getThreatLevelClass(level) {
    switch (level) {
        case 'critical': return 'danger';
        case 'high': return 'warning';
        case 'medium': return 'info';
        case 'low': return 'success';
        default: return 'secondary';
    }
}

function getSeverityClass(severity) {
    switch (severity) {
        case 'critical': return 'danger';
        case 'high': return 'warning';
        case 'medium': return 'info';
        case 'low': return 'success';
        default: return 'secondary';
    }
}

// 更新趋势图表
function updateTrendChart(trendData) {
    const ctx = document.getElementById('trend-chart');
    if (!ctx) return;

    // 如果图表已存在，先销毁
    if (window.trendChart) {
        window.trendChart.destroy();
    }

    const labels = trendData.map(point => {
        const date = new Date(point.timestamp);
        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    });

    const attackCounts = trendData.map(point => point.attack_count);
    const uniqueIPs = trendData.map(point => point.unique_ips);

    window.trendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '攻击次数',
                data: attackCounts,
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                tension: 0.1,
                yAxisID: 'y'
            }, {
                label: '唯一IP数',
                data: uniqueIPs,
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                tension: 0.1,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: '时间'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '攻击次数'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: '唯一IP数'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            plugins: {
                title: {
                    display: true,
                    text: '攻击趋势分析'
                },
                legend: {
                    display: true
                }
            }
        }
    });
}

// 格式化日期时间
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 初始化分析页面
function initializeAnalysisPage() {
    // 加载所有分析数据
    loadAttackTrendAnalysis();
    loadIPGeographicAnalysis();
    loadAttackTypeAnalysis();

    // 绑定时间范围选择器
    const timeRangeSelectors = document.querySelectorAll('.time-range-selector');
    timeRangeSelectors.forEach(selector => {
        selector.addEventListener('change', function() {
            const timeRange = this.value;
            const nodeId = document.getElementById('node-filter')?.value || '';

            // 重新加载所有分析数据
            loadAttackTrendAnalysis(timeRange, nodeId);
            loadIPGeographicAnalysis(timeRange, nodeId);
            loadAttackTypeAnalysis(timeRange, nodeId);
        });
    });

    // 绑定节点过滤器
    const nodeFilter = document.getElementById('node-filter');
    if (nodeFilter) {
        nodeFilter.addEventListener('change', function() {
            const nodeId = this.value;
            const timeRange = document.querySelector('.time-range-selector')?.value || '24h';

            // 重新加载所有分析数据
            loadAttackTrendAnalysis(timeRange, nodeId);
            loadIPGeographicAnalysis(timeRange, nodeId);
            loadAttackTypeAnalysis(timeRange, nodeId);
        });
    }
}
