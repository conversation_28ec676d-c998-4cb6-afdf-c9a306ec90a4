// 部署管理页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    checkAuth();
    
    // 加载用户信息
    loadUserInfo();
    
    // 加载部署数据
    loadDeploymentsData();
    
    // 加载节点和模板选项
    loadSelectOptions();
    
    // 绑定事件
    bindEvents();
    
    // 设置定时刷新
    setInterval(loadDeploymentsData, 30000); // 30秒刷新一次
    
    // 设置导航激活状态
    setActiveNavLink();
});

// 侧边栏切换功能
function toggleSidebar() {
    const sidebar = document.getElementById('mainSidebar');
    if (sidebar) {
        sidebar.classList.toggle('show');
    }
}

// 用户菜单切换
function toggleUserMenu() {
    // 用户菜单切换逻辑
    console.log('Toggle user menu');
}

// 设置导航激活状态
function setActiveNavLink() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
}

// 全局变量
let currentPage = 1;
let pageSize = 10;
let totalPages = 1;
let selectedDeployments = new Set();

// 检查认证状态
function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = '/login';
        return;
    }
}

// 加载用户信息
function loadUserInfo() {
    const userInfo = localStorage.getItem('user_info');
    if (userInfo) {
        try {
            const user = JSON.parse(userInfo);
            document.getElementById('username').textContent = user.username || '用户';
        } catch (error) {
            console.error('Parse user info failed:', error);
        }
    }
}

// 加载部署数据
async function loadDeploymentsData() {
    try {
        // 加载统计数据
        await loadDeploymentStatistics();
        
        // 加载部署列表
        await loadDeploymentsList();
        
    } catch (error) {
        console.error('Load deployments data failed:', error);
        showError('加载数据失败: ' + error.message);
    }
}

// 加载部署统计
async function loadDeploymentStatistics() {
    try {
        const response = await fetch('/api/v1/deployments/statistics', {
            headers: getAuthHeaders()
        });
        
        const data = await handleApiResponse(response);
        const stats = data.data;
        
        // 更新统计数据
        document.getElementById('total-deployments').textContent = stats.total || 0;
        document.getElementById('running-deployments').textContent = stats.running || 0;
        document.getElementById('stopped-deployments').textContent = stats.stopped || 0;
        document.getElementById('failed-deployments').textContent = stats.failed || 0;
        
    } catch (error) {
        console.error('Load deployment statistics failed:', error);
        // 显示默认值
        document.getElementById('total-deployments').textContent = '0';
        document.getElementById('running-deployments').textContent = '0';
        document.getElementById('stopped-deployments').textContent = '0';
        document.getElementById('failed-deployments').textContent = '0';
    }
}

// 加载部署列表
async function loadDeploymentsList() {
    try {
        const searchKeyword = document.getElementById('search-input').value;
        const statusFilter = document.getElementById('status-filter').value;
        const nodeFilter = document.getElementById('node-filter').value;
        const templateFilter = document.getElementById('template-filter').value;
        
        const params = new URLSearchParams({
            page: currentPage,
            size: pageSize
        });
        
        if (searchKeyword) params.append('keyword', searchKeyword);
        if (statusFilter) params.append('status', statusFilter);
        if (nodeFilter) params.append('node_id', nodeFilter);
        if (templateFilter) params.append('template_id', templateFilter);
        
        const response = await fetch(`/api/v1/deployments?${params}`, {
            headers: getAuthHeaders()
        });
        
        const data = await handleApiResponse(response);
        const deployments = data.data || [];
        const pagination = data.pagination || {};
        
        // 更新部署表格
        updateDeploymentsTable(deployments);
        
        // 更新分页
        updatePagination(pagination);
        
    } catch (error) {
        console.error('Load deployments list failed:', error);
        updateDeploymentsTable([]);
    }
}

// 更新部署表格
function updateDeploymentsTable(deployments) {
    const tbody = document.querySelector('#deployments-table tbody');
    
    if (deployments.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center text-muted">
                    <i class="bi bi-inbox me-2"></i>暂无部署数据
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = deployments.map(deployment => {
        const statusBadge = getStatusBadge(deployment.status);
        const containerID = deployment.container_id ? 
            `<code>${deployment.container_id.substring(0, 12)}</code>` : 
            '<span class="text-muted">-</span>';
        
        return `
            <tr>
                <td>
                    <input type="checkbox" class="deployment-checkbox" value="${deployment.id}" 
                           onchange="toggleDeploymentSelection('${deployment.id}')">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="bi bi-play-circle me-2 text-primary"></i>
                        ${deployment.name}
                    </div>
                </td>
                <td>
                    <span class="badge bg-secondary">${deployment.node_id}</span>
                </td>
                <td>
                    <span class="badge bg-info">模板 ${deployment.template_id}</span>
                </td>
                <td>${statusBadge}</td>
                <td>${containerID}</td>
                <td>${formatTime(deployment.created_at)}</td>
                <td>${formatTime(deployment.updated_at)}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewDeployment('${deployment.id}')" 
                                title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${getActionButtons(deployment)}
                        <button class="btn btn-outline-danger" onclick="deleteDeployment('${deployment.id}')" 
                                title="删除部署">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// 获取状态徽章
function getStatusBadge(status) {
    const badges = {
        'running': '<span class="badge bg-success"><i class="bi bi-play-circle me-1"></i>运行中</span>',
        'stopped': '<span class="badge bg-warning"><i class="bi bi-pause-circle me-1"></i>已停止</span>',
        'failed': '<span class="badge bg-danger"><i class="bi bi-x-circle me-1"></i>失败</span>',
        'pending': '<span class="badge bg-info"><i class="bi bi-hourglass-split me-1"></i>等待中</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">未知</span>';
}

// 获取操作按钮
function getActionButtons(deployment) {
    if (deployment.status === 'running') {
        return `
            <button class="btn btn-outline-warning" onclick="stopDeployment('${deployment.id}')" 
                    title="停止服务">
                <i class="bi bi-pause"></i>
            </button>
            <button class="btn btn-outline-info" onclick="restartDeployment('${deployment.id}')" 
                    title="重启服务">
                <i class="bi bi-arrow-clockwise"></i>
            </button>
        `;
    } else if (deployment.status === 'stopped') {
        return `
            <button class="btn btn-outline-success" onclick="startDeployment('${deployment.id}')" 
                    title="启动服务">
                <i class="bi bi-play"></i>
            </button>
        `;
    } else {
        return `
            <button class="btn btn-outline-secondary" disabled title="无可用操作">
                <i class="bi bi-dash"></i>
            </button>
        `;
    }
}

// 格式化时间
function formatTime(timeStr) {
    if (!timeStr) return '未知';
    
    try {
        const time = new Date(timeStr);
        return time.toLocaleString();
    } catch (error) {
        return '无效时间';
    }
}

// 更新分页
function updatePagination(pagination) {
    const paginationElement = document.getElementById('pagination');
    
    if (!pagination.total || pagination.total === 0) {
        paginationElement.innerHTML = '';
        return;
    }
    
    totalPages = pagination.total_pages || 1;
    currentPage = pagination.page || 1;
    
    let paginationHTML = '';
    
    // 上一页
    paginationHTML += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                <i class="bi bi-chevron-left"></i>
            </a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    paginationHTML += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                <i class="bi bi-chevron-right"></i>
            </a>
        </li>
    `;
    
    paginationElement.innerHTML = paginationHTML;
}

// 切换页面
function changePage(page) {
    if (page < 1 || page > totalPages || page === currentPage) {
        return;
    }
    
    currentPage = page;
    loadDeploymentsList();
}

// 加载选择框选项
async function loadSelectOptions() {
    try {
        // 加载节点选项
        const nodesResponse = await fetch('/api/v1/nodes', {
            headers: getAuthHeaders()
        });
        const nodesData = await handleApiResponse(nodesResponse);
        const nodes = nodesData.data || [];
        
        // 更新节点选择框
        const nodeSelects = ['node-filter', 'deployment-node'];
        nodeSelects.forEach(selectId => {
            const select = document.getElementById(selectId);
            if (select) {
                const currentValue = select.value;
                const options = nodes.map(node => 
                    `<option value="${node.id}">${node.name} (${node.ip})</option>`
                ).join('');
                
                if (selectId === 'node-filter') {
                    select.innerHTML = '<option value="">所有节点</option>' + options;
                } else {
                    select.innerHTML = '<option value="">选择节点</option>' + options;
                }
                
                select.value = currentValue;
            }
        });
        
        // 加载模板选项
        const templatesResponse = await fetch('/api/v1/templates', {
            headers: getAuthHeaders()
        });
        const templatesData = await handleApiResponse(templatesResponse);
        const templates = templatesData.data || [];
        
        // 更新模板选择框
        const templateSelects = ['template-filter', 'deployment-template'];
        templateSelects.forEach(selectId => {
            const select = document.getElementById(selectId);
            if (select) {
                const currentValue = select.value;
                const options = templates.map(template => 
                    `<option value="${template.id}">${template.name} (${template.type})</option>`
                ).join('');
                
                if (selectId === 'template-filter') {
                    select.innerHTML = '<option value="">所有模板</option>' + options;
                } else {
                    select.innerHTML = '<option value="">选择模板</option>' + options;
                }
                
                select.value = currentValue;
            }
        });
        
    } catch (error) {
        console.error('Load select options failed:', error);
    }
}

// 绑定事件
function bindEvents() {
    // 退出登录
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }

    // 搜索输入
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                currentPage = 1;
                loadDeploymentsList();
            }, 500);
        });
    }

    // 过滤器
    ['status-filter', 'node-filter', 'template-filter'].forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) {
            filter.addEventListener('change', function() {
                currentPage = 1;
                loadDeploymentsList();
            });
        }
    });

    // 全选复选框
    const selectAllCheckbox = document.getElementById('select-all');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            toggleSelectAll(this.checked);
        });
    }
}

// 切换部署选择
function toggleDeploymentSelection(deploymentId) {
    if (selectedDeployments.has(deploymentId)) {
        selectedDeployments.delete(deploymentId);
    } else {
        selectedDeployments.add(deploymentId);
    }

    updateSelectAllState();
}

// 切换全选
function toggleSelectAll(checked) {
    const checkboxes = document.querySelectorAll('.deployment-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = checked;
        if (checked) {
            selectedDeployments.add(checkbox.value);
        } else {
            selectedDeployments.delete(checkbox.value);
        }
    });
}

// 更新全选状态
function updateSelectAllState() {
    const checkboxes = document.querySelectorAll('.deployment-checkbox');
    const selectAllCheckbox = document.getElementById('select-all');

    if (checkboxes.length === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
        return;
    }

    const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;

    if (checkedCount === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedCount === checkboxes.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    }
}

// 刷新部署
function refreshDeployments() {
    const refreshBtn = document.querySelector('button[onclick="refreshDeployments()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>刷新中...';
    refreshBtn.disabled = true;

    loadDeploymentsData().finally(() => {
        setTimeout(() => {
            refreshBtn.innerHTML = originalText;
            refreshBtn.disabled = false;
        }, 1000);
    });
}

// 创建部署
async function createDeployment() {
    const form = document.getElementById('create-deployment-form');

    const deploymentData = {
        name: document.getElementById('deployment-name').value,
        node_id: document.getElementById('deployment-node').value,
        template_id: parseInt(document.getElementById('deployment-template').value),
        ports: document.getElementById('deployment-ports').value.split(',').filter(p => p.trim()),
        environment: {},
        volumes: document.getElementById('deployment-volumes').value.split(',').filter(v => v.trim())
    };

    // 解析环境变量
    const envText = document.getElementById('deployment-env').value.trim();
    if (envText) {
        try {
            deploymentData.environment = JSON.parse(envText);
        } catch (error) {
            showError('环境变量JSON格式无效');
            return;
        }
    }

    // 验证表单
    if (!deploymentData.name || !deploymentData.node_id || !deploymentData.template_id) {
        showError('请填写所有必填字段');
        return;
    }

    try {
        const response = await fetch('/api/v1/deployments', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify(deploymentData)
        });

        const data = await handleApiResponse(response);

        showSuccess('部署创建成功');

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('createDeploymentModal'));
        modal.hide();

        // 重置表单
        form.reset();

        // 刷新列表
        loadDeploymentsData();

    } catch (error) {
        console.error('Create deployment failed:', error);
        showError('创建部署失败: ' + error.message);
    }
}

// 查看部署详情
function viewDeployment(deploymentId) {
    showInfo(`查看部署 ${deploymentId} 的详情功能正在开发中`);
}

// 启动部署
async function startDeployment(deploymentId) {
    try {
        const response = await fetch(`/api/v1/deployments/${deploymentId}/start`, {
            method: 'POST',
            headers: getAuthHeaders()
        });

        await handleApiResponse(response);

        showSuccess('部署启动命令已发送');
        loadDeploymentsData();

    } catch (error) {
        console.error('Start deployment failed:', error);
        showError('启动部署失败: ' + error.message);
    }
}

// 停止部署
async function stopDeployment(deploymentId) {
    if (!confirm('确定要停止这个部署吗？')) {
        return;
    }

    try {
        const response = await fetch(`/api/v1/deployments/${deploymentId}/stop`, {
            method: 'POST',
            headers: getAuthHeaders()
        });

        await handleApiResponse(response);

        showSuccess('部署停止命令已发送');
        loadDeploymentsData();

    } catch (error) {
        console.error('Stop deployment failed:', error);
        showError('停止部署失败: ' + error.message);
    }
}

// 重启部署
async function restartDeployment(deploymentId) {
    if (!confirm('确定要重启这个部署吗？')) {
        return;
    }

    try {
        const response = await fetch(`/api/v1/deployments/${deploymentId}/restart`, {
            method: 'POST',
            headers: getAuthHeaders()
        });

        await handleApiResponse(response);

        showSuccess('部署重启命令已发送');
        loadDeploymentsData();

    } catch (error) {
        console.error('Restart deployment failed:', error);
        showError('重启部署失败: ' + error.message);
    }
}

// 删除部署
async function deleteDeployment(deploymentId) {
    if (!confirm('确定要删除这个部署吗？此操作不可恢复。')) {
        return;
    }

    try {
        const response = await fetch(`/api/v1/deployments/${deploymentId}`, {
            method: 'DELETE',
            headers: getAuthHeaders()
        });

        await handleApiResponse(response);

        showSuccess('部署删除成功');
        loadDeploymentsData();

    } catch (error) {
        console.error('Delete deployment failed:', error);
        showError('删除部署失败: ' + error.message);
    }
}

// 批量操作
function batchStart() {
    if (selectedDeployments.size === 0) {
        showError('请先选择要启动的部署');
        return;
    }

    showInfo(`批量启动功能正在开发中，已选择 ${selectedDeployments.size} 个部署`);
}

function batchStop() {
    if (selectedDeployments.size === 0) {
        showError('请先选择要停止的部署');
        return;
    }

    showInfo(`批量停止功能正在开发中，已选择 ${selectedDeployments.size} 个部署`);
}

function batchDelete() {
    if (selectedDeployments.size === 0) {
        showError('请先选择要删除的部署');
        return;
    }

    if (!confirm(`确定要删除选中的 ${selectedDeployments.size} 个部署吗？此操作不可恢复。`)) {
        return;
    }

    showInfo(`批量删除功能正在开发中，已选择 ${selectedDeployments.size} 个部署`);
}

// 导出部署数据
function exportDeployments() {
    showInfo('导出功能正在开发中');
}

// 刷新所有状态
function refreshAll() {
    showInfo('刷新所有状态功能正在开发中');
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token');
        localStorage.removeItem('user_info');
        window.location.href = '/login';
    }
}

// 工具函数：获取认证头
function getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };
}

// 工具函数：处理API响应
async function handleApiResponse(response) {
    if (!response.ok) {
        if (response.status === 401) {
            localStorage.removeItem('token');
            localStorage.removeItem('user_info');
            window.location.href = '/login';
            return;
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    if (!data.success) {
        throw new Error(data.message || '请求失败');
    }

    return data;
}

// 显示消息函数
function showError(message) {
    showAlert(message, 'danger');
}

function showSuccess(message) {
    showAlert(message, 'success');
}

function showInfo(message) {
    showAlert(message, 'info');
}

function showAlert(message, type) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alert);

    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, type === 'success' ? 3000 : 5000);
}
