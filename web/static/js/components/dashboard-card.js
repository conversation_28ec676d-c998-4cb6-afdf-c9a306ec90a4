/**
 * Dashboard Card Web Component
 * 可复用的仪表板卡片组件
 */

class DashboardCard extends HTMLElement {
    constructor() {
        super();
        this.attachShadow({ mode: 'open' });
    }

    static get observedAttributes() {
        return ['title', 'value', 'icon', 'color', 'trend', 'loading'];
    }

    connectedCallback() {
        this.render();
        this.setupEventListeners();
    }

    attributeChangedCallback(name, oldValue, newValue) {
        if (oldValue !== newValue) {
            this.render();
        }
    }

    get title() {
        return this.getAttribute('title') || '';
    }

    get value() {
        return this.getAttribute('value') || '';
    }

    get icon() {
        return this.getAttribute('icon') || '';
    }

    get color() {
        return this.getAttribute('color') || 'primary';
    }

    get trend() {
        return this.getAttribute('trend') || '';
    }

    get loading() {
        return this.hasAttribute('loading');
    }

    render() {
        const colorMap = {
            primary: 'var(--color-primary-500)',
            success: 'var(--color-success-500)',
            warning: 'var(--color-warning-500)',
            danger: 'var(--color-danger-500)',
            info: 'var(--color-info-500)'
        };

        const trendIcon = this.getTrendIcon();
        const trendColor = this.getTrendColor();

        this.shadowRoot.innerHTML = `
            <style>
                :host {
                    display: block;
                    --card-color: ${colorMap[this.color] || colorMap.primary};
                }

                .card {
                    background: var(--color-surface);
                    border: 1px solid var(--color-border);
                    border-radius: var(--radius-lg);
                    padding: var(--space-6);
                    box-shadow: var(--shadow-sm);
                    transition: var(--transition-base);
                    position: relative;
                    overflow: hidden;
                }

                .card:hover {
                    box-shadow: var(--shadow-md);
                    transform: translateY(-2px);
                }

                .card::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: var(--card-color);
                }

                .card-header {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: var(--space-4);
                }

                .card-title {
                    font-size: var(--font-size-sm);
                    font-weight: var(--font-weight-medium);
                    color: var(--color-text-secondary);
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                    margin: 0;
                }

                .card-icon {
                    width: 2rem;
                    height: 2rem;
                    color: var(--card-color);
                    opacity: 0.8;
                }

                .card-body {
                    display: flex;
                    align-items: baseline;
                    justify-content: space-between;
                }

                .card-value {
                    font-size: var(--font-size-3xl);
                    font-weight: var(--font-weight-bold);
                    color: var(--color-text-primary);
                    margin: 0;
                    line-height: 1;
                }

                .card-trend {
                    display: flex;
                    align-items: center;
                    gap: var(--space-1);
                    font-size: var(--font-size-sm);
                    font-weight: var(--font-weight-medium);
                    color: ${trendColor};
                }

                .trend-icon {
                    width: 1rem;
                    height: 1rem;
                }

                .loading {
                    position: relative;
                }

                .loading::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(
                        90deg,
                        transparent,
                        rgba(255, 255, 255, 0.4),
                        transparent
                    );
                    animation: shimmer 1.5s infinite;
                }

                @keyframes shimmer {
                    0% { transform: translateX(-100%); }
                    100% { transform: translateX(100%); }
                }

                .animate-in {
                    animation: slideInUp 0.3s ease-out;
                }

                @keyframes slideInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
            </style>

            <div class="card ${this.loading ? 'loading' : ''} animate-in">
                <div class="card-header">
                    <h3 class="card-title">${this.title}</h3>
                    ${this.icon ? `<div class="card-icon">${this.getIconSVG()}</div>` : ''}
                </div>
                <div class="card-body">
                    <div class="card-value">${this.loading ? '---' : this.value}</div>
                    ${this.trend && !this.loading ? `
                        <div class="card-trend">
                            <span class="trend-icon">${trendIcon}</span>
                            <span>${this.trend}</span>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    getIconSVG() {
        // 简化的图标映射，实际项目中可以从图标管理器获取
        const icons = {
            users: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/></svg>',
            server: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z"/></svg>',
            chart: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M3 3v18h18v-2H5V3H3zm4 14h2V9H7v8zm4 0h2V7h-2v10zm4 0h2v-4h-2v4z"/></svg>',
            shield: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/></svg>'
        };
        return icons[this.icon] || icons.chart;
    }

    getTrendIcon() {
        if (!this.trend) return '';
        
        const value = parseFloat(this.trend);
        if (value > 0) {
            return '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M7 14l5-5 5 5z"/></svg>';
        } else if (value < 0) {
            return '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M7 10l5 5 5-5z"/></svg>';
        }
        return '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M8 12h8"/></svg>';
    }

    getTrendColor() {
        if (!this.trend) return 'var(--color-text-muted)';
        
        const value = parseFloat(this.trend);
        if (value > 0) {
            return 'var(--color-success-600)';
        } else if (value < 0) {
            return 'var(--color-danger-600)';
        }
        return 'var(--color-text-muted)';
    }

    setupEventListeners() {
        this.shadowRoot.querySelector('.card').addEventListener('click', () => {
            this.dispatchEvent(new CustomEvent('card-click', {
                detail: {
                    title: this.title,
                    value: this.value,
                    color: this.color
                },
                bubbles: true
            }));
        });
    }

    // 公共方法
    updateValue(newValue, trend = null) {
        this.setAttribute('value', newValue);
        if (trend !== null) {
            this.setAttribute('trend', trend);
        }
    }

    setLoading(loading) {
        if (loading) {
            this.setAttribute('loading', '');
        } else {
            this.removeAttribute('loading');
        }
    }
}

// 注册组件
customElements.define('dashboard-card', DashboardCard);

export { DashboardCard };
