/**
 * Loading Spinner Web Component
 * 现代化加载动画组件
 */

class LoadingSpinner extends HTMLElement {
    constructor() {
        super();
        this.attachShadow({ mode: 'open' });
    }

    static get observedAttributes() {
        return ['size', 'color', 'type', 'text'];
    }

    connectedCallback() {
        this.render();
    }

    attributeChangedCallback(name, oldValue, newValue) {
        if (oldValue !== newValue) {
            this.render();
        }
    }

    get size() {
        return this.getAttribute('size') || 'md';
    }

    get color() {
        return this.getAttribute('color') || 'primary';
    }

    get type() {
        return this.getAttribute('type') || 'spinner';
    }

    get text() {
        return this.getAttribute('text') || '';
    }

    render() {
        const sizeMap = {
            xs: '1rem',
            sm: '1.5rem',
            md: '2rem',
            lg: '3rem',
            xl: '4rem'
        };

        const colorMap = {
            primary: 'var(--color-primary-500)',
            success: 'var(--color-success-500)',
            warning: 'var(--color-warning-500)',
            danger: 'var(--color-danger-500)',
            info: 'var(--color-info-500)',
            gray: 'var(--color-gray-500)'
        };

        const spinnerSize = sizeMap[this.size] || sizeMap.md;
        const spinnerColor = colorMap[this.color] || colorMap.primary;

        this.shadowRoot.innerHTML = `
            <style>
                :host {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    gap: var(--space-2);
                    --spinner-size: ${spinnerSize};
                    --spinner-color: ${spinnerColor};
                }

                .loading-container {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: var(--space-3);
                }

                .spinner {
                    width: var(--spinner-size);
                    height: var(--spinner-size);
                    position: relative;
                }

                /* 默认旋转动画 */
                .spinner-default {
                    border: 2px solid var(--color-gray-200);
                    border-top: 2px solid var(--spinner-color);
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }

                /* 脉冲动画 */
                .spinner-pulse {
                    background: var(--spinner-color);
                    border-radius: 50%;
                    animation: pulse 1.5s ease-in-out infinite;
                }

                /* 点动画 */
                .spinner-dots {
                    display: flex;
                    gap: 0.25rem;
                    align-items: center;
                    justify-content: center;
                }

                .spinner-dots .dot {
                    width: calc(var(--spinner-size) / 4);
                    height: calc(var(--spinner-size) / 4);
                    background: var(--spinner-color);
                    border-radius: 50%;
                    animation: bounce 1.4s ease-in-out infinite both;
                }

                .spinner-dots .dot:nth-child(1) { animation-delay: -0.32s; }
                .spinner-dots .dot:nth-child(2) { animation-delay: -0.16s; }
                .spinner-dots .dot:nth-child(3) { animation-delay: 0s; }

                /* 波浪动画 */
                .spinner-wave {
                    display: flex;
                    gap: 0.125rem;
                    align-items: center;
                    justify-content: center;
                }

                .spinner-wave .bar {
                    width: calc(var(--spinner-size) / 8);
                    height: var(--spinner-size);
                    background: var(--spinner-color);
                    border-radius: calc(var(--spinner-size) / 16);
                    animation: wave 1.2s ease-in-out infinite;
                }

                .spinner-wave .bar:nth-child(1) { animation-delay: -1.1s; }
                .spinner-wave .bar:nth-child(2) { animation-delay: -1.0s; }
                .spinner-wave .bar:nth-child(3) { animation-delay: -0.9s; }
                .spinner-wave .bar:nth-child(4) { animation-delay: -0.8s; }
                .spinner-wave .bar:nth-child(5) { animation-delay: -0.7s; }

                /* 环形动画 */
                .spinner-ring {
                    border: 2px solid transparent;
                    border-top: 2px solid var(--spinner-color);
                    border-right: 2px solid var(--spinner-color);
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }

                .loading-text {
                    font-size: var(--font-size-sm);
                    color: var(--color-text-secondary);
                    font-weight: var(--font-weight-medium);
                    text-align: center;
                }

                /* 动画定义 */
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }

                @keyframes pulse {
                    0%, 100% {
                        transform: scale(0);
                        opacity: 1;
                    }
                    50% {
                        transform: scale(1);
                        opacity: 0.7;
                    }
                }

                @keyframes bounce {
                    0%, 80%, 100% {
                        transform: scale(0);
                    }
                    40% {
                        transform: scale(1);
                    }
                }

                @keyframes wave {
                    0%, 40%, 100% {
                        transform: scaleY(0.4);
                    }
                    20% {
                        transform: scaleY(1);
                    }
                }

                /* 全屏覆盖模式 */
                :host([overlay]) {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(255, 255, 255, 0.9);
                    z-index: var(--z-modal);
                    backdrop-filter: blur(2px);
                }

                :host([overlay]) .loading-container {
                    height: 100vh;
                    justify-content: center;
                }

                /* 响应式调整 */
                @media (prefers-reduced-motion: reduce) {
                    .spinner-default,
                    .spinner-ring {
                        animation: none;
                        border-top-color: var(--spinner-color);
                        border-right-color: var(--spinner-color);
                    }
                    
                    .spinner-pulse,
                    .spinner-dots .dot,
                    .spinner-wave .bar {
                        animation: none;
                        opacity: 0.7;
                    }
                }
            </style>

            <div class="loading-container">
                <div class="spinner">
                    ${this.getSpinnerHTML()}
                </div>
                ${this.text ? `<div class="loading-text">${this.text}</div>` : ''}
            </div>
        `;
    }

    getSpinnerHTML() {
        switch (this.type) {
            case 'pulse':
                return '<div class="spinner-pulse"></div>';
            
            case 'dots':
                return `
                    <div class="spinner-dots">
                        <div class="dot"></div>
                        <div class="dot"></div>
                        <div class="dot"></div>
                    </div>
                `;
            
            case 'wave':
                return `
                    <div class="spinner-wave">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                `;
            
            case 'ring':
                return '<div class="spinner-ring"></div>';
            
            default:
                return '<div class="spinner-default"></div>';
        }
    }

    // 公共方法
    static show(options = {}) {
        const spinner = document.createElement('loading-spinner');
        
        if (options.size) spinner.setAttribute('size', options.size);
        if (options.color) spinner.setAttribute('color', options.color);
        if (options.type) spinner.setAttribute('type', options.type);
        if (options.text) spinner.setAttribute('text', options.text);
        if (options.overlay) spinner.setAttribute('overlay', '');

        if (options.overlay) {
            document.body.appendChild(spinner);
        }

        return spinner;
    }

    static hide() {
        const overlaySpinners = document.querySelectorAll('loading-spinner[overlay]');
        overlaySpinners.forEach(spinner => spinner.remove());
    }
}

// 注册组件
customElements.define('loading-spinner', LoadingSpinner);

export { LoadingSpinner };
