/**
 * Notification Web Component
 * 现代化通知组件
 */

class NotificationComponent extends HTMLElement {
    constructor() {
        super();
        this.attachShadow({ mode: 'open' });
        this.autoHideTimer = null;
    }

    static get observedAttributes() {
        return ['type', 'title', 'message', 'duration', 'closable'];
    }

    connectedCallback() {
        this.render();
        this.setupEventListeners();
        this.startAutoHide();
    }

    disconnectedCallback() {
        if (this.autoHideTimer) {
            clearTimeout(this.autoHideTimer);
        }
    }

    attributeChangedCallback(name, oldValue, newValue) {
        if (oldValue !== newValue) {
            this.render();
        }
    }

    get type() {
        return this.getAttribute('type') || 'info';
    }

    get title() {
        return this.getAttribute('title') || '';
    }

    get message() {
        return this.getAttribute('message') || '';
    }

    get duration() {
        return parseInt(this.getAttribute('duration')) || 5000;
    }

    get closable() {
        return this.hasAttribute('closable');
    }

    render() {
        const typeConfig = {
            success: {
                color: 'var(--color-success-500)',
                bgColor: 'var(--color-success-50)',
                borderColor: 'var(--color-success-200)',
                textColor: 'var(--color-success-800)',
                icon: this.getSuccessIcon()
            },
            error: {
                color: 'var(--color-danger-500)',
                bgColor: 'var(--color-danger-50)',
                borderColor: 'var(--color-danger-200)',
                textColor: 'var(--color-danger-800)',
                icon: this.getErrorIcon()
            },
            warning: {
                color: 'var(--color-warning-500)',
                bgColor: 'var(--color-warning-50)',
                borderColor: 'var(--color-warning-200)',
                textColor: 'var(--color-warning-800)',
                icon: this.getWarningIcon()
            },
            info: {
                color: 'var(--color-info-500)',
                bgColor: 'var(--color-info-50)',
                borderColor: 'var(--color-info-200)',
                textColor: 'var(--color-info-800)',
                icon: this.getInfoIcon()
            }
        };

        const config = typeConfig[this.type] || typeConfig.info;

        this.shadowRoot.innerHTML = `
            <style>
                :host {
                    display: block;
                    --notification-color: ${config.color};
                    --notification-bg: ${config.bgColor};
                    --notification-border: ${config.borderColor};
                    --notification-text: ${config.textColor};
                }

                .notification {
                    display: flex;
                    align-items: flex-start;
                    gap: var(--space-3);
                    padding: var(--space-4);
                    background: var(--notification-bg);
                    border: 1px solid var(--notification-border);
                    border-left: 4px solid var(--notification-color);
                    border-radius: var(--radius-md);
                    box-shadow: var(--shadow-sm);
                    margin-bottom: var(--space-3);
                    position: relative;
                    overflow: hidden;
                    animation: slideInRight 0.3s ease-out;
                }

                .notification.closing {
                    animation: slideOutRight 0.3s ease-in forwards;
                }

                .notification-icon {
                    flex-shrink: 0;
                    width: 1.25rem;
                    height: 1.25rem;
                    color: var(--notification-color);
                    margin-top: 0.125rem;
                }

                .notification-content {
                    flex: 1;
                    min-width: 0;
                }

                .notification-title {
                    font-size: var(--font-size-sm);
                    font-weight: var(--font-weight-semibold);
                    color: var(--notification-text);
                    margin: 0 0 var(--space-1) 0;
                    line-height: 1.4;
                }

                .notification-message {
                    font-size: var(--font-size-sm);
                    color: var(--notification-text);
                    margin: 0;
                    line-height: 1.4;
                    opacity: 0.9;
                }

                .notification-close {
                    flex-shrink: 0;
                    background: none;
                    border: none;
                    color: var(--notification-text);
                    cursor: pointer;
                    padding: var(--space-1);
                    border-radius: var(--radius-sm);
                    opacity: 0.7;
                    transition: var(--transition-fast);
                    width: 1.5rem;
                    height: 1.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .notification-close:hover {
                    opacity: 1;
                    background: rgba(0, 0, 0, 0.1);
                }

                .notification-close svg {
                    width: 1rem;
                    height: 1rem;
                }

                .progress-bar {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    height: 2px;
                    background: var(--notification-color);
                    transition: width linear;
                    opacity: 0.6;
                }

                @keyframes slideInRight {
                    from {
                        opacity: 0;
                        transform: translateX(100%);
                    }
                    to {
                        opacity: 1;
                        transform: translateX(0);
                    }
                }

                @keyframes slideOutRight {
                    from {
                        opacity: 1;
                        transform: translateX(0);
                    }
                    to {
                        opacity: 0;
                        transform: translateX(100%);
                    }
                }
            </style>

            <div class="notification">
                <div class="notification-icon">
                    ${config.icon}
                </div>
                <div class="notification-content">
                    ${this.title ? `<div class="notification-title">${this.title}</div>` : ''}
                    ${this.message ? `<div class="notification-message">${this.message}</div>` : ''}
                </div>
                ${this.closable ? `
                    <button class="notification-close" aria-label="关闭通知">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                        </svg>
                    </button>
                ` : ''}
                ${this.duration > 0 ? '<div class="progress-bar"></div>' : ''}
            </div>
        `;
    }

    getSuccessIcon() {
        return `<svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
        </svg>`;
    }

    getErrorIcon() {
        return `<svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>`;
    }

    getWarningIcon() {
        return `<svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
        </svg>`;
    }

    getInfoIcon() {
        return `<svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
        </svg>`;
    }

    setupEventListeners() {
        const closeBtn = this.shadowRoot.querySelector('.notification-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.close());
        }

        // 点击通知本身也可以关闭（可选）
        this.shadowRoot.querySelector('.notification').addEventListener('click', (e) => {
            if (e.target.closest('.notification-close')) return;
            
            this.dispatchEvent(new CustomEvent('notification-click', {
                detail: {
                    type: this.type,
                    title: this.title,
                    message: this.message
                },
                bubbles: true
            }));
        });
    }

    startAutoHide() {
        if (this.duration > 0) {
            const progressBar = this.shadowRoot.querySelector('.progress-bar');
            if (progressBar) {
                progressBar.style.width = '100%';
                progressBar.style.transitionDuration = `${this.duration}ms`;
                
                // 立即开始动画
                requestAnimationFrame(() => {
                    progressBar.style.width = '0%';
                });
            }

            this.autoHideTimer = setTimeout(() => {
                this.close();
            }, this.duration);
        }
    }

    close() {
        const notification = this.shadowRoot.querySelector('.notification');
        notification.classList.add('closing');
        
        setTimeout(() => {
            this.dispatchEvent(new CustomEvent('notification-close', {
                detail: { type: this.type },
                bubbles: true
            }));
            this.remove();
        }, 300);
    }

    // 公共方法
    static show(options = {}) {
        const notification = document.createElement('notification-component');
        
        if (options.type) notification.setAttribute('type', options.type);
        if (options.title) notification.setAttribute('title', options.title);
        if (options.message) notification.setAttribute('message', options.message);
        if (options.duration !== undefined) notification.setAttribute('duration', options.duration);
        if (options.closable) notification.setAttribute('closable', '');

        // 添加到通知容器
        let container = document.querySelector('.notification-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 1rem;
                right: 1rem;
                z-index: 9999;
                max-width: 400px;
                width: 100%;
            `;
            document.body.appendChild(container);
        }

        container.appendChild(notification);
        return notification;
    }
}

// 注册组件
customElements.define('notification-component', NotificationComponent);

export { NotificationComponent };
