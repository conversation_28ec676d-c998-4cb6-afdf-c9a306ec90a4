/**
 * Web Components 入口文件
 * 统一导出所有组件
 */

// 导入所有组件
import { DashboardCard } from './dashboard-card.js';
import { NotificationComponent } from './notification.js';
import { LoadingSpinner } from './loading-spinner.js';

// 组件工厂函数
export const Components = {
    // Dashboard Card 快捷创建
    createDashboardCard(options = {}) {
        const card = document.createElement('dashboard-card');
        
        if (options.title) card.setAttribute('title', options.title);
        if (options.value) card.setAttribute('value', options.value);
        if (options.icon) card.setAttribute('icon', options.icon);
        if (options.color) card.setAttribute('color', options.color);
        if (options.trend) card.setAttribute('trend', options.trend);
        if (options.loading) card.setAttribute('loading', '');

        return card;
    },

    // 通知快捷创建
    showNotification(options = {}) {
        return NotificationComponent.show(options);
    },

    // 成功通知
    showSuccess(message, title = '成功') {
        return this.showNotification({
            type: 'success',
            title,
            message,
            duration: 3000,
            closable: true
        });
    },

    // 错误通知
    showError(message, title = '错误') {
        return this.showNotification({
            type: 'error',
            title,
            message,
            duration: 5000,
            closable: true
        });
    },

    // 警告通知
    showWarning(message, title = '警告') {
        return this.showNotification({
            type: 'warning',
            title,
            message,
            duration: 4000,
            closable: true
        });
    },

    // 信息通知
    showInfo(message, title = '提示') {
        return this.showNotification({
            type: 'info',
            title,
            message,
            duration: 3000,
            closable: true
        });
    },

    // 加载器快捷创建
    createLoader(options = {}) {
        return LoadingSpinner.show(options);
    },

    // 显示全屏加载
    showLoading(text = '加载中...') {
        return LoadingSpinner.show({
            overlay: true,
            text,
            size: 'lg',
            type: 'spinner'
        });
    },

    // 隐藏全屏加载
    hideLoading() {
        LoadingSpinner.hide();
    }
};

// 全局工具函数
export const Utils = {
    // 等待组件定义完成
    async waitForComponent(tagName) {
        if (customElements.get(tagName)) {
            return Promise.resolve();
        }
        
        return new Promise((resolve) => {
            customElements.whenDefined(tagName).then(resolve);
        });
    },

    // 批量等待组件
    async waitForComponents(tagNames) {
        const promises = tagNames.map(name => this.waitForComponent(name));
        return Promise.all(promises);
    },

    // 检查组件是否已注册
    isComponentRegistered(tagName) {
        return !!customElements.get(tagName);
    },

    // 获取所有已注册的组件
    getRegisteredComponents() {
        return [
            'dashboard-card',
            'notification-component', 
            'loading-spinner'
        ].filter(name => this.isComponentRegistered(name));
    }
};

// 自动初始化函数
export async function initializeComponents() {
    console.log('🚀 初始化 Web Components...');
    
    try {
        // 等待所有组件定义完成
        await Utils.waitForComponents([
            'dashboard-card',
            'notification-component',
            'loading-spinner'
        ]);
        
        console.log('✅ Web Components 初始化完成');
        
        // 触发自定义事件
        document.dispatchEvent(new CustomEvent('components-ready', {
            detail: {
                components: Utils.getRegisteredComponents()
            }
        }));
        
    } catch (error) {
        console.error('❌ Web Components 初始化失败:', error);
    }
}

// DOM 加载完成后自动初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeComponents);
} else {
    initializeComponents();
}

// 导出所有组件类
export {
    DashboardCard,
    NotificationComponent,
    LoadingSpinner
};

// 全局暴露（可选，用于非模块环境）
if (typeof window !== 'undefined') {
    window.HoneypotComponents = {
        Components,
        Utils,
        DashboardCard,
        NotificationComponent,
        LoadingSpinner,
        initializeComponents
    };
}
