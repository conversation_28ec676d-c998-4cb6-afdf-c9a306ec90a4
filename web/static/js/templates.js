// 模板管理页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    checkAuth();
    
    // 加载用户信息
    loadUserInfo();
    
    // 加载模板数据
    loadTemplatesData();
    
    // 绑定事件
    bindEvents();
    
    // 设置定时刷新
    setInterval(loadTemplatesData, 30000); // 30秒刷新一次
    
    // 设置导航激活状态
    setActiveNavLink();
});

// 侧边栏切换功能
function toggleSidebar() {
    const sidebar = document.getElementById('mainSidebar');
    if (sidebar) {
        sidebar.classList.toggle('show');
    }
}

// 用户菜单切换
function toggleUserMenu() {
    // 用户菜单切换逻辑
    console.log('Toggle user menu');
}

// 设置导航激活状态
function setActiveNavLink() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
}

// 全局变量
let currentPage = 1;
let pageSize = 10;
let totalPages = 1;
let selectedTemplates = new Set();
let currentViewMode = 'grid'; // grid 或 list

// 检查认证状态
function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = '/login';
        return;
    }
}

// 加载用户信息
function loadUserInfo() {
    const userInfo = localStorage.getItem('user_info');
    if (userInfo) {
        try {
            const user = JSON.parse(userInfo);
            document.getElementById('username').textContent = user.username || '用户';
        } catch (error) {
            console.error('Parse user info failed:', error);
        }
    }
}

// 加载模板数据
async function loadTemplatesData() {
    try {
        // 加载统计数据
        await loadTemplateStatistics();
        
        // 加载模板列表
        await loadTemplatesList();
        
    } catch (error) {
        console.error('Load templates data failed:', error);
        showError('加载数据失败: ' + error.message);
    }
}

// 加载模板统计
async function loadTemplateStatistics() {
    try {
        const response = await fetch('/api/v1/dashboard/overview', {
            headers: getAuthHeaders()
        });
        
        const data = await handleApiResponse(response);
        const overview = data.data;
        
        // 更新统计数据
        document.getElementById('total-templates').textContent = overview.templates.total || 0;
        document.getElementById('active-templates').textContent = overview.templates.active || 0;
        document.getElementById('total-deployments').textContent = overview.deployments.total || 0;
        document.getElementById('template-types').textContent = overview.templates.types || 0;
        
    } catch (error) {
        console.error('Load template statistics failed:', error);
        // 显示默认值
        document.getElementById('total-templates').textContent = '0';
        document.getElementById('active-templates').textContent = '0';
        document.getElementById('total-deployments').textContent = '0';
        document.getElementById('template-types').textContent = '0';
    }
}

// 加载模板列表
async function loadTemplatesList() {
    try {
        const searchKeyword = document.getElementById('search-input').value;
        const typeFilter = document.getElementById('type-filter').value;
        const statusFilter = document.getElementById('status-filter').value;
        
        const params = new URLSearchParams({
            page: currentPage,
            size: pageSize
        });
        
        if (searchKeyword) params.append('keyword', searchKeyword);
        if (typeFilter) params.append('type', typeFilter);
        if (statusFilter) params.append('status', statusFilter);
        
        const response = await fetch(`/api/v1/templates?${params}`, {
            headers: getAuthHeaders()
        });
        
        const data = await handleApiResponse(response);
        const templates = data.data || [];
        const pagination = data.pagination || {};
        
        // 更新模板显示
        if (currentViewMode === 'grid') {
            updateTemplatesGrid(templates);
        } else {
            updateTemplatesTable(templates);
        }
        
        // 更新分页
        updatePagination(pagination);
        
    } catch (error) {
        console.error('Load templates list failed:', error);
        if (currentViewMode === 'grid') {
            updateTemplatesGrid([]);
        } else {
            updateTemplatesTable([]);
        }
    }
}

// 更新模板网格视图
function updateTemplatesGrid(templates) {
    const grid = document.getElementById('templates-grid');
    
    if (templates.length === 0) {
        grid.innerHTML = `
            <div class="col-12 text-center text-muted py-5">
                <i class="bi bi-inbox display-1 mb-3"></i>
                <h5>暂无模板数据</h5>
                <p>点击"创建模板"按钮添加第一个模板</p>
            </div>
        `;
        return;
    }
    
    grid.innerHTML = templates.map(template => {
        const statusBadge = getStatusBadge(template.status);
        const typeBadge = getTypeBadge(template.type);
        const deploymentCount = template.deployment_count || 0;
        
        return `
            <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                <div class="card h-100 shadow-sm template-card" data-template-id="${template.id}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            ${typeBadge}
                            <span class="ms-2 fw-bold">${template.name}</span>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="viewTemplate('${template.id}')">
                                    <i class="bi bi-eye me-2"></i>查看详情
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="deployTemplate('${template.id}')">
                                    <i class="bi bi-play-circle me-2"></i>部署模板
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="editTemplate('${template.id}')">
                                    <i class="bi bi-pencil me-2"></i>编辑模板
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteTemplate('${template.id}')">
                                    <i class="bi bi-trash me-2"></i>删除模板
                                </a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            ${statusBadge}
                            <span class="badge bg-secondary ms-1">v${template.version}</span>
                        </div>
                        <p class="card-text text-muted small">${template.description || '暂无描述'}</p>
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="text-primary fw-bold">${deploymentCount}</div>
                                <div class="small text-muted">部署次数</div>
                            </div>
                            <div class="col-6">
                                <div class="text-info fw-bold">${formatTime(template.created_at)}</div>
                                <div class="small text-muted">创建时间</div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button class="btn btn-outline-primary btn-sm" onclick="viewTemplate('${template.id}')">
                                <i class="bi bi-eye me-1"></i>查看
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="deployTemplate('${template.id}')">
                                <i class="bi bi-play-circle me-1"></i>部署
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// 更新模板表格视图
function updateTemplatesTable(templates) {
    const tbody = document.querySelector('#templates-table tbody');
    
    if (templates.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-muted">
                    <i class="bi bi-inbox me-2"></i>暂无模板数据
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = templates.map(template => {
        const statusBadge = getStatusBadge(template.status);
        const typeBadge = getTypeBadge(template.type);
        const deploymentCount = template.deployment_count || 0;
        
        return `
            <tr>
                <td>
                    <input type="checkbox" class="template-checkbox" value="${template.id}" 
                           onchange="toggleTemplateSelection('${template.id}')">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="bi bi-layers me-2 text-primary"></i>
                        ${template.name}
                    </div>
                </td>
                <td>${typeBadge}</td>
                <td><span class="badge bg-secondary">v${template.version}</span></td>
                <td>${statusBadge}</td>
                <td><span class="badge bg-info">${deploymentCount}</span></td>
                <td>${formatTime(template.created_at)}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewTemplate('${template.id}')" 
                                title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="deployTemplate('${template.id}')" 
                                title="部署模板">
                            <i class="bi bi-play-circle"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteTemplate('${template.id}')" 
                                title="删除模板">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// 获取状态徽章
function getStatusBadge(status) {
    const badges = {
        'active': '<span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>活跃</span>',
        'inactive': '<span class="badge bg-secondary"><i class="bi bi-pause-circle me-1"></i>非活跃</span>',
        'processing': '<span class="badge bg-warning"><i class="bi bi-hourglass-split me-1"></i>处理中</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">未知</span>';
}

// 获取类型徽章
function getTypeBadge(type) {
    const badges = {
        'ssh': '<span class="badge bg-primary"><i class="bi bi-terminal me-1"></i>SSH</span>',
        'web': '<span class="badge bg-success"><i class="bi bi-globe me-1"></i>Web</span>',
        'ftp': '<span class="badge bg-info"><i class="bi bi-folder me-1"></i>FTP</span>',
        'telnet': '<span class="badge bg-warning"><i class="bi bi-terminal me-1"></i>Telnet</span>',
        'custom': '<span class="badge bg-secondary"><i class="bi bi-gear me-1"></i>自定义</span>'
    };
    return badges[type] || '<span class="badge bg-secondary">未知</span>';
}

// 格式化时间
function formatTime(timeStr) {
    if (!timeStr) return '未知';
    
    try {
        const time = new Date(timeStr);
        return time.toLocaleDateString();
    } catch (error) {
        return '无效时间';
    }
}

// 更新分页
function updatePagination(pagination) {
    const paginationElement = document.getElementById('pagination');
    
    if (!pagination.total || pagination.total === 0) {
        paginationElement.innerHTML = '';
        return;
    }
    
    totalPages = pagination.total_pages || 1;
    currentPage = pagination.page || 1;
    
    let paginationHTML = '';
    
    // 上一页
    paginationHTML += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                <i class="bi bi-chevron-left"></i>
            </a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    paginationHTML += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                <i class="bi bi-chevron-right"></i>
            </a>
        </li>
    `;
    
    paginationElement.innerHTML = paginationHTML;
}

// 切换页面
function changePage(page) {
    if (page < 1 || page > totalPages || page === currentPage) {
        return;
    }

    currentPage = page;
    loadTemplatesList();
}

// 绑定事件
function bindEvents() {
    // 退出登录
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }

    // 搜索输入
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                currentPage = 1;
                loadTemplatesList();
            }, 500);
        });
    }

    // 类型过滤
    const typeFilter = document.getElementById('type-filter');
    if (typeFilter) {
        typeFilter.addEventListener('change', function() {
            currentPage = 1;
            loadTemplatesList();
        });
    }

    // 状态过滤
    const statusFilter = document.getElementById('status-filter');
    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            currentPage = 1;
            loadTemplatesList();
        });
    }

    // 视图模式切换
    const gridViewBtn = document.getElementById('grid-view');
    const listViewBtn = document.getElementById('list-view');

    if (gridViewBtn && listViewBtn) {
        gridViewBtn.addEventListener('change', function() {
            if (this.checked) {
                switchViewMode('grid');
            }
        });

        listViewBtn.addEventListener('change', function() {
            if (this.checked) {
                switchViewMode('list');
            }
        });
    }

    // 全选复选框
    const selectAllCheckbox = document.getElementById('select-all');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            toggleSelectAll(this.checked);
        });
    }
}

// 切换视图模式
function switchViewMode(mode) {
    currentViewMode = mode;
    const gridView = document.getElementById('templates-grid');
    const listView = document.getElementById('templates-list');

    if (mode === 'grid') {
        gridView.classList.remove('d-none');
        listView.classList.add('d-none');
        pageSize = 12;
    } else {
        gridView.classList.add('d-none');
        listView.classList.remove('d-none');
        pageSize = 10;
    }

    currentPage = 1;
    loadTemplatesList();
}

// 切换模板选择
function toggleTemplateSelection(templateId) {
    if (selectedTemplates.has(templateId)) {
        selectedTemplates.delete(templateId);
    } else {
        selectedTemplates.add(templateId);
    }

    updateSelectAllState();
}

// 切换全选
function toggleSelectAll(checked) {
    const checkboxes = document.querySelectorAll('.template-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = checked;
        if (checked) {
            selectedTemplates.add(checkbox.value);
        } else {
            selectedTemplates.delete(checkbox.value);
        }
    });
}

// 更新全选状态
function updateSelectAllState() {
    const checkboxes = document.querySelectorAll('.template-checkbox');
    const selectAllCheckbox = document.getElementById('select-all');

    if (checkboxes.length === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
        return;
    }

    const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;

    if (checkedCount === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedCount === checkboxes.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    }
}

// 刷新模板
function refreshTemplates() {
    const refreshBtn = document.querySelector('button[onclick="refreshTemplates()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>刷新中...';
    refreshBtn.disabled = true;

    loadTemplatesData().finally(() => {
        setTimeout(() => {
            refreshBtn.innerHTML = originalText;
            refreshBtn.disabled = false;
        }, 1000);
    });
}

// 添加模板
async function addTemplate() {
    const form = document.getElementById('add-template-form');

    const templateData = {
        name: document.getElementById('template-name').value,
        type: document.getElementById('template-type').value,
        image_name: document.getElementById('template-image').value.split(':')[0] || '',
        image_tag: document.getElementById('template-image').value.split(':')[1] || 'latest',
        version: document.getElementById('template-version').value,
        description: document.getElementById('template-description').value,
        config: document.getElementById('template-config').value
    };

    // 验证表单
    if (!templateData.name || !templateData.type || !templateData.image_name || !templateData.version) {
        showError('请填写所有必填字段');
        return;
    }

    // 验证JSON配置
    if (templateData.config) {
        try {
            JSON.parse(templateData.config);
        } catch (error) {
            showError('配置JSON格式无效');
            return;
        }
    }

    try {
        const response = await fetch('/api/v1/templates', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify(templateData)
        });

        const data = await handleApiResponse(response);

        showSuccess('模板创建成功');

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('addTemplateModal'));
        modal.hide();

        // 重置表单
        form.reset();

        // 刷新列表
        loadTemplatesData();

    } catch (error) {
        console.error('Add template failed:', error);
        showError('创建模板失败: ' + error.message);
    }
}

// 查看模板详情
function viewTemplate(templateId) {
    showInfo(`查看模板 ${templateId} 的详情功能正在开发中`);
}

// 部署模板
function deployTemplate(templateId) {
    showInfo(`部署模板 ${templateId} 的功能正在开发中`);
}

// 编辑模板
function editTemplate(templateId) {
    showInfo(`编辑模板 ${templateId} 的功能正在开发中`);
}

// 删除模板
async function deleteTemplate(templateId) {
    if (!confirm('确定要删除这个模板吗？此操作不可恢复。')) {
        return;
    }

    try {
        const response = await fetch(`/api/v1/templates/${templateId}`, {
            method: 'DELETE',
            headers: getAuthHeaders()
        });

        await handleApiResponse(response);

        showSuccess('模板删除成功');
        loadTemplatesData();

    } catch (error) {
        console.error('Delete template failed:', error);
        showError('删除模板失败: ' + error.message);
    }
}

// 导出模板数据
function exportTemplates() {
    showInfo('导出功能正在开发中');
}

// 批量操作
function batchOperation() {
    if (selectedTemplates.size === 0) {
        showError('请先选择要操作的模板');
        return;
    }

    showInfo(`批量操作功能正在开发中，已选择 ${selectedTemplates.size} 个模板`);
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token');
        localStorage.removeItem('user_info');
        window.location.href = '/login';
    }
}

// 工具函数：获取认证头
function getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };
}

// 工具函数：处理API响应
async function handleApiResponse(response) {
    if (!response.ok) {
        if (response.status === 401) {
            localStorage.removeItem('token');
            localStorage.removeItem('user_info');
            window.location.href = '/login';
            return;
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    if (!data.success) {
        throw new Error(data.message || '请求失败');
    }

    return data;
}

// 显示消息函数
function showError(message) {
    showAlert(message, 'danger');
}

function showSuccess(message) {
    showAlert(message, 'success');
}

function showInfo(message) {
    showAlert(message, 'info');
}

function showAlert(message, type) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alert);

    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, type === 'success' ? 3000 : 5000);
}
