/**
 * 主JavaScript入口文件
 * 现代化ES6模块架构
 */

// 导入核心模块
import { iconManager } from './modules/icon-manager.js';
import { Components, Utils, initializeComponents } from './components/index.js';

/**
 * 应用主类
 */
class HoneypotApp {
    constructor() {
        this.isInitialized = false;
        this.modules = new Map();
        this.components = Components;
        this.utils = Utils;
    }

    /**
     * 初始化应用
     */
    async init() {
        if (this.isInitialized) {
            console.warn('应用已经初始化');
            return;
        }

        console.log('🚀 初始化蜜罐管理平台...');

        try {
            // 1. 初始化Web Components
            await initializeComponents();

            // 2. 初始化图标管理器
            await this.initIconManager();

            // 3. 初始化事件监听器
            this.initEventListeners();

            // 4. 初始化页面特定功能
            await this.initPageSpecific();

            // 5. 初始化全局功能
            this.initGlobalFeatures();

            this.isInitialized = true;
            console.log('✅ 蜜罐管理平台初始化完成');

            // 触发应用就绪事件
            this.dispatchEvent('app-ready');

        } catch (error) {
            console.error('❌ 应用初始化失败:', error);
            this.components.showError('应用初始化失败，请刷新页面重试');
        }
    }

    /**
     * 初始化图标管理器
     */
    async initIconManager() {
        try {
            await iconManager.preloadIcons();
            await iconManager.replaceIcons();
            console.log('✅ 图标系统初始化完成');
        } catch (error) {
            console.warn('⚠️ 图标系统初始化失败:', error);
        }
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 全局错误处理
        window.addEventListener('error', (event) => {
            console.error('全局错误:', event.error);
            this.components.showError('发生了一个错误，请稍后重试');
        });

        // 未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            console.error('未处理的Promise拒绝:', event.reason);
            this.components.showError('操作失败，请稍后重试');
        });

        // 网络状态变化
        window.addEventListener('online', () => {
            this.components.showSuccess('网络连接已恢复');
        });

        window.addEventListener('offline', () => {
            this.components.showWarning('网络连接已断开');
        });

        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                this.onPageVisible();
            } else {
                this.onPageHidden();
            }
        });

        // 组件事件监听
        document.addEventListener('card-click', (event) => {
            console.log('卡片点击:', event.detail);
        });

        document.addEventListener('notification-click', (event) => {
            console.log('通知点击:', event.detail);
        });
    }

    /**
     * 初始化页面特定功能
     */
    async initPageSpecific() {
        const currentPage = this.getCurrentPage();
        
        switch (currentPage) {
            case 'dashboard':
                await this.initDashboard();
                break;
            case 'nodes':
                await this.initNodes();
                break;
            case 'templates':
                await this.initTemplates();
                break;
            case 'login':
                await this.initLogin();
                break;
            default:
                console.log(`页面 ${currentPage} 无需特殊初始化`);
        }
    }

    /**
     * 初始化全局功能
     */
    initGlobalFeatures() {
        // 初始化侧边栏
        this.initSidebar();

        // 初始化主题切换
        this.initThemeToggle();

        // 初始化搜索功能
        this.initSearch();

        // 初始化快捷键
        this.initKeyboardShortcuts();
    }

    /**
     * 获取当前页面
     */
    getCurrentPage() {
        const path = window.location.pathname;
        if (path.includes('/dashboard')) return 'dashboard';
        if (path.includes('/nodes')) return 'nodes';
        if (path.includes('/templates')) return 'templates';
        if (path.includes('/login')) return 'login';
        return 'unknown';
    }

    /**
     * 初始化仪表板
     */
    async initDashboard() {
        console.log('📊 初始化仪表板...');
        
        // 动态加载仪表板模块
        try {
            const { DashboardManager } = await import('./modules/dashboard-manager.js');
            const dashboardManager = new DashboardManager();
            await dashboardManager.init();
            this.modules.set('dashboard', dashboardManager);
        } catch (error) {
            console.warn('仪表板模块加载失败:', error);
        }
    }

    /**
     * 初始化节点管理
     */
    async initNodes() {
        console.log('🖥️ 初始化节点管理...');
        // TODO: 实现节点管理初始化
    }

    /**
     * 初始化模板管理
     */
    async initTemplates() {
        console.log('📦 初始化模板管理...');
        // TODO: 实现模板管理初始化
    }

    /**
     * 初始化登录页面
     */
    async initLogin() {
        console.log('🔐 初始化登录页面...');
        // TODO: 实现登录页面初始化
    }

    /**
     * 初始化侧边栏
     */
    initSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const toggleBtn = document.querySelector('.sidebar-toggle');
        
        if (toggleBtn && sidebar) {
            toggleBtn.addEventListener('click', () => {
                sidebar.classList.toggle('show');
            });
        }

        // 移动端自动隐藏侧边栏
        if (window.innerWidth <= 768) {
            document.addEventListener('click', (event) => {
                if (!sidebar.contains(event.target) && !toggleBtn.contains(event.target)) {
                    sidebar.classList.remove('show');
                }
            });
        }
    }

    /**
     * 初始化主题切换
     */
    initThemeToggle() {
        const themeToggle = document.querySelector('.theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                document.documentElement.classList.toggle('dark');
                const isDark = document.documentElement.classList.contains('dark');
                localStorage.setItem('theme', isDark ? 'dark' : 'light');
            });
        }

        // 恢复主题设置
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.documentElement.classList.add('dark');
        }
    }

    /**
     * 初始化搜索功能
     */
    initSearch() {
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            let searchTimeout;
            
            searchInput.addEventListener('input', (event) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.performSearch(event.target.value);
                }, 300);
            });
        }
    }

    /**
     * 初始化快捷键
     */
    initKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ctrl/Cmd + K 打开搜索
            if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
                event.preventDefault();
                const searchInput = document.querySelector('.search-input');
                if (searchInput) {
                    searchInput.focus();
                }
            }

            // ESC 关闭模态框
            if (event.key === 'Escape') {
                const modals = document.querySelectorAll('.modal.show');
                modals.forEach(modal => {
                    modal.classList.remove('show');
                });
            }
        });
    }

    /**
     * 执行搜索
     */
    performSearch(query) {
        if (query.length < 2) return;
        
        console.log('搜索:', query);
        // TODO: 实现搜索功能
    }

    /**
     * 页面可见时的处理
     */
    onPageVisible() {
        console.log('页面变为可见');
        // 可以在这里刷新数据
    }

    /**
     * 页面隐藏时的处理
     */
    onPageHidden() {
        console.log('页面变为隐藏');
        // 可以在这里暂停定时器等
    }

    /**
     * 触发自定义事件
     */
    dispatchEvent(eventName, detail = {}) {
        document.dispatchEvent(new CustomEvent(eventName, {
            detail: { ...detail, app: this }
        }));
    }

    /**
     * 获取模块
     */
    getModule(name) {
        return this.modules.get(name);
    }

    /**
     * 销毁应用
     */
    destroy() {
        this.modules.forEach(module => {
            if (module.destroy) {
                module.destroy();
            }
        });
        this.modules.clear();
        this.isInitialized = false;
    }
}

// 创建应用实例
const app = new HoneypotApp();

// 自动初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => app.init());
} else {
    app.init();
}

// 全局暴露
window.HoneypotApp = app;

// 导出应用实例
export default app;
