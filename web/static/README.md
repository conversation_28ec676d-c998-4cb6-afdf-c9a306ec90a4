# Web Static 目录结构说明

## 📁 目录组织

### `/assets/` - 静态资源
- `fonts/` - 字体文件 (Inter, 中文字体等)
- `icons/` - SVG图标库 (本地化图标)
- `images/` - 图片资源 (logo, 背景图等)

### `/css/` - 样式文件
- `base/` - 基础样式 (reset, variables, typography)
- `components/` - 组件样式 (按组件分类)
- `layouts/` - 布局样式 (grid, flexbox layouts)
- `themes/` - 主题样式 (light/dark themes)
- `utils/` - 工具类样式 (utilities, helpers)

### `/js/` - JavaScript文件
- `components/` - Web Components (自定义元素)
- `modules/` - ES6模块 (功能模块)
- `utils/` - 工具函数 (helpers, utilities)
- `main.js` - 应用入口文件

### `/libs/` - 第三方库
- `bootstrap/` - Bootstrap框架
- `chart.js/` - 图表库
- `bootstrap-icons/` - Bootstrap图标
- `local/` - 本地化的第三方资源

## 🎯 设计原则

1. **模块化** - 按功能和类型组织文件
2. **可维护性** - 清晰的文件命名和结构
3. **性能优化** - 支持代码分割和懒加载
4. **本地化** - 所有资源本地存储，无外部依赖

## 📝 命名规范

- 文件名使用kebab-case: `dashboard-card.js`
- CSS类名使用BEM: `.card__header--active`
- JavaScript模块使用PascalCase: `ChartManager`
- 常量使用UPPER_SNAKE_CASE: `API_BASE_URL`
