#!/bin/bash

# 提取项目中使用的Font Awesome图标
# 基于base.html中的图标使用情况

ICON_SOURCE="/root/Honeypot-AdminPanel/web/static/libs/font-awesome/svgs/solid"
ICON_DEST="/root/Honeypot-AdminPanel/web/static/assets/icons"

# 创建目标目录
mkdir -p "$ICON_DEST"

# 项目中使用的图标列表 (Font Awesome 6.0 名称)
ICONS=(
    "shield-halved"
    "gauge-high"
    "server"
    "cube"
    "triangle-exclamation"
    "user"
    "user-gear"
    "right-from-bracket"
    "right-to-bracket"
    "house"
    "eye"
    "heart-pulse"
    "rocket"
    "bug"
    "chart-line"
    "file-lines"
    "users"
    "gear"
    "list"
    "circle-check"
    "circle-exclamation"
    "circle-info"
)

echo "正在提取图标..."

for icon in "${ICONS[@]}"; do
    if [ -f "$ICON_SOURCE/$icon.svg" ]; then
        cp "$ICON_SOURCE/$icon.svg" "$ICON_DEST/"
        echo "✓ 已复制: $icon.svg"
    else
        echo "✗ 未找到: $icon.svg"
    fi
done

echo "图标提取完成！"
echo "图标位置: $ICON_DEST"
