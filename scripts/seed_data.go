package main

import (
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"os"
	"time"

	"honeypot-admin/internal/database"
	"honeypot-admin/internal/models"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

func main() {
	log.Println("开始填充蜜罐管理平台模拟数据...")

	// 初始化数据库连接
	config := database.DefaultConfig()
	
	// 从环境变量覆盖配置
	if host := os.Getenv("DB_HOST"); host != "" {
		config.Host = host
	}
	if port := os.Getenv("DB_PORT"); port != "" {
		fmt.Sscanf(port, "%d", &config.Port)
	}
	if user := os.Getenv("DB_USER"); user != "" {
		config.Username = user
	}
	if password := os.Getenv("DB_PASSWORD"); password != "" {
		config.Password = password
	}
	if dbname := os.Getenv("DB_NAME"); dbname != "" {
		config.DBName = dbname
	}

	if err := database.Initialize(config); err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}
	defer database.Close()

	db := database.GetDB()

	// 设置随机种子
	rand.Seed(time.Now().UnixNano())

	// 填充数据
	if err := seedUsers(db); err != nil {
		log.Printf("填充用户数据失败: %v", err)
	}

	if err := seedNodes(db); err != nil {
		log.Printf("填充节点数据失败: %v", err)
	}

	if err := seedTemplates(db); err != nil {
		log.Printf("填充模板数据失败: %v", err)
	}

	if err := seedDeployments(db); err != nil {
		log.Printf("填充部署数据失败: %v", err)
	}

	if err := seedAttackEvents(db); err != nil {
		log.Printf("填充攻击事件数据失败: %v", err)
	}

	log.Println("模拟数据填充完成!")
}

// seedUsers 填充用户数据
func seedUsers(db *gorm.DB) error {
	log.Println("填充用户数据...")

	users := []models.User{
		{
			Username: "operator1",
			Email:    "<EMAIL>",
			Role:     models.RoleOperator,
			Status:   models.StatusActive,
		},
		{
			Username: "operator2", 
			Email:    "<EMAIL>",
			Role:     models.RoleOperator,
			Status:   models.StatusActive,
		},
		{
			Username: "observer1",
			Email:    "<EMAIL>", 
			Role:     models.RoleObserver,
			Status:   models.StatusActive,
		},
		{
			Username: "observer2",
			Email:    "<EMAIL>",
			Role:     models.RoleObserver,
			Status:   models.StatusInactive,
		},
		{
			Username: "testuser",
			Email:    "<EMAIL>",
			Role:     models.RoleOperator,
			Status:   models.StatusActive,
		},
	}

	// 为所有用户设置默认密码 "password123"
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte("password123"), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("密码加密失败: %w", err)
	}

	for i := range users {
		users[i].Password = string(hashedPassword)
		
		// 设置随机的最后登录时间
		if rand.Float32() < 0.7 { // 70%的用户有登录记录
			lastLogin := time.Now().Add(-time.Duration(rand.Intn(30*24)) * time.Hour)
			users[i].LastLoginAt = &lastLogin
		}

		// 检查用户是否已存在
		var existingUser models.User
		if err := db.Where("username = ?", users[i].Username).First(&existingUser).Error; err == gorm.ErrRecordNotFound {
			if err := db.Create(&users[i]).Error; err != nil {
				log.Printf("创建用户 %s 失败: %v", users[i].Username, err)
			} else {
				log.Printf("创建用户: %s", users[i].Username)
			}
		}
	}

	return nil
}

// seedNodes 填充节点数据
func seedNodes(db *gorm.DB) error {
	log.Println("填充节点数据...")

	regions := []string{"华东", "华北", "华南", "西南", "东北"}
	statuses := []models.NodeStatus{models.NodeStatusOnline, models.NodeStatusOffline, models.NodeStatusError}
	
	nodes := []models.Node{
		{
			ID:          "node-beijing-001",
			Name:        "北京节点-001",
			IP:          "************",
			Token:       generateToken(),
			Status:      models.NodeStatusOnline,
			Region:      "华北",
			Description: "北京数据中心主节点",
		},
		{
			ID:          "node-shanghai-001", 
			Name:        "上海节点-001",
			IP:          "************",
			Token:       generateToken(),
			Status:      models.NodeStatusOnline,
			Region:      "华东",
			Description: "上海数据中心主节点",
		},
		{
			ID:          "node-guangzhou-001",
			Name:        "广州节点-001", 
			IP:          "************",
			Token:       generateToken(),
			Status:      models.NodeStatusOffline,
			Region:      "华南",
			Description: "广州数据中心主节点",
		},
		{
			ID:          "node-chengdu-001",
			Name:        "成都节点-001",
			IP:          "************", 
			Token:       generateToken(),
			Status:      models.NodeStatusOnline,
			Region:      "西南",
			Description: "成都数据中心主节点",
		},
		{
			ID:          "node-shenyang-001",
			Name:        "沈阳节点-001",
			IP:          "************",
			Token:       generateToken(),
			Status:      models.NodeStatusError,
			Region:      "东北", 
			Description: "沈阳数据中心主节点",
		},
	}

	// 生成更多随机节点
	for i := 6; i <= 15; i++ {
		region := regions[rand.Intn(len(regions))]
		status := statuses[rand.Intn(len(statuses))]
		
		node := models.Node{
			ID:          fmt.Sprintf("node-auto-%03d", i),
			Name:        fmt.Sprintf("自动节点-%03d", i),
			IP:          fmt.Sprintf("192.168.%d.%d", rand.Intn(10)+2, rand.Intn(200)+10),
			Token:       generateToken(),
			Status:      status,
			Region:      region,
			Description: fmt.Sprintf("自动生成的%s节点", region),
		}

		// 设置心跳时间
		if status == models.NodeStatusOnline {
			heartbeat := time.Now().Add(-time.Duration(rand.Intn(300)) * time.Second)
			node.LastHeartbeatAt = &heartbeat
		}

		nodes = append(nodes, node)
	}

	for _, node := range nodes {
		var existingNode models.Node
		if err := db.Where("id = ?", node.ID).First(&existingNode).Error; err == gorm.ErrRecordNotFound {
			if err := db.Create(&node).Error; err != nil {
				log.Printf("创建节点 %s 失败: %v", node.ID, err)
			} else {
				log.Printf("创建节点: %s", node.Name)
			}
		}
	}

	return nil
}

// seedTemplates 填充模板数据
func seedTemplates(db *gorm.DB) error {
	log.Println("填充模板数据...")

	templates := []models.Template{
		{
			ID:          "template_ftp_001",
			Name:        "FTP蜜罐模板",
			Type:        models.TemplateTypeFTP,
			Description: "基于vsftpd的FTP蜜罐服务",
			ImageName:   "honeypot/ftp-vsftpd",
			ImageTag:    "latest",
			Version:     "1.0.0",
			Status:      models.TemplateStatusActive,
			Active:      true,
			ConfigSchema: models.ConfigSchema{
				"port": models.ConfigParam{
					Type:        "integer",
					Description: "FTP端口",
					Default:     21,
					Required:    true,
				},
				"banner": models.ConfigParam{
					Type:        "string",
					Description: "FTP欢迎横幅",
					Default:     "Welcome to FTP Server",
					Required:    false,
				},
				"anonymous_enabled": models.ConfigParam{
					Type:        "boolean",
					Description: "是否允许匿名登录",
					Default:     true,
					Required:    false,
				},
			},
			DefaultConfig: models.DefaultConfig{
				"port":              21,
				"banner":            "Welcome to FTP Server",
				"anonymous_enabled": true,
			},
			ResourceRequirements: &models.ResourceRequirements{
				Memory: "64Mi",
				CPU:    "50m",
			},
		},
		{
			ID:          "template_telnet_001",
			Name:        "Telnet蜜罐模板",
			Type:        models.TemplateTypeTelnet,
			Description: "基于Cowrie的Telnet蜜罐服务",
			ImageName:   "honeypot/telnet-cowrie",
			ImageTag:    "latest",
			Version:     "1.0.0",
			Status:      models.TemplateStatusActive,
			Active:      true,
			ConfigSchema: models.ConfigSchema{
				"port": models.ConfigParam{
					Type:        "integer",
					Description: "Telnet端口",
					Default:     23,
					Required:    true,
				},
				"hostname": models.ConfigParam{
					Type:        "string",
					Description: "主机名",
					Default:     "server",
					Required:    false,
				},
			},
			DefaultConfig: models.DefaultConfig{
				"port":     23,
				"hostname": "server",
			},
			ResourceRequirements: &models.ResourceRequirements{
				Memory: "128Mi",
				CPU:    "100m",
			},
		},
		{
			ID:          "template_smtp_001",
			Name:        "SMTP蜜罐模板",
			Type:        models.TemplateTypeSMTP,
			Description: "基于Postfix的SMTP蜜罐服务",
			ImageName:   "honeypot/smtp-postfix",
			ImageTag:    "latest",
			Version:     "1.0.0",
			Status:      models.TemplateStatusActive,
			Active:      true,
			ConfigSchema: models.ConfigSchema{
				"port": models.ConfigParam{
					Type:        "integer",
					Description: "SMTP端口",
					Default:     25,
					Required:    true,
				},
				"domain": models.ConfigParam{
					Type:        "string",
					Description: "邮件域名",
					Default:     "example.com",
					Required:    true,
				},
			},
			DefaultConfig: models.DefaultConfig{
				"port":   25,
				"domain": "example.com",
			},
			ResourceRequirements: &models.ResourceRequirements{
				Memory: "256Mi",
				CPU:    "200m",
			},
		},
		{
			ID:          "template_mysql_001",
			Name:        "MySQL蜜罐模板",
			Type:        models.TemplateTypeDatabase,
			Description: "基于MySQL的数据库蜜罐服务",
			ImageName:   "honeypot/mysql-fake",
			ImageTag:    "latest",
			Version:     "1.0.0",
			Status:      models.TemplateStatusActive,
			Active:      true,
			ConfigSchema: models.ConfigSchema{
				"port": models.ConfigParam{
					Type:        "integer",
					Description: "MySQL端口",
					Default:     3306,
					Required:    true,
				},
				"version": models.ConfigParam{
					Type:        "string",
					Description: "MySQL版本",
					Default:     "5.7.30",
					Required:    false,
				},
			},
			DefaultConfig: models.DefaultConfig{
				"port":    3306,
				"version": "5.7.30",
			},
			ResourceRequirements: &models.ResourceRequirements{
				Memory: "512Mi",
				CPU:    "300m",
			},
		},
	}

	for _, template := range templates {
		var existingTemplate models.Template
		if err := db.Where("id = ?", template.ID).First(&existingTemplate).Error; err == gorm.ErrRecordNotFound {
			if err := db.Create(&template).Error; err != nil {
				log.Printf("创建模板 %s 失败: %v", template.ID, err)
			} else {
				log.Printf("创建模板: %s", template.Name)
			}
		}
	}

	return nil
}

// generateToken 生成随机token
func generateToken() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, 32)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}
